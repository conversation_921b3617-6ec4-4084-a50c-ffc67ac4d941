# FELLRec科研小白专用详细说明

## 🎯 这个项目是做什么的？

想象一下，你在网上购物时，网站会推荐你可能喜欢的商品。这个项目就是在研究如何让这种推荐系统变得更聪明、更保护隐私。

### 核心创新点
1. **联邦学习** - 多个用户的数据不需要集中，就能训练出好的推荐模型
2. **大语言模型** - 使用像ChatGPT这样的AI模型来做推荐
3. **隐私保护** - 用户数据不离开本地设备

## 🧠 核心概念解释

### 1. 什么是联邦学习？
**传统方式**：所有用户数据收集到一个地方训练模型
```
用户A数据 ──┐
用户B数据 ──┼──→ 中央服务器 ──→ 训练模型
用户C数据 ──┘
```

**联邦学习**：数据留在本地，只共享模型参数
```
用户A ──→ 本地训练 ──┐
用户B ──→ 本地训练 ──┼──→ 聚合模型参数 ──→ 全局模型
用户C ──→ 本地训练 ──┘
```

### 2. 什么是LoRA？
LoRA (Low-Rank Adaptation) 是一种聪明的训练技巧：
- **问题**：大语言模型有几十亿参数，全部训练太费资源
- **解决**：只训练一小部分新增参数（1-2%），效果几乎一样
- **好处**：省显存、省时间、省钱

### 3. 什么是推荐系统？
根据用户的历史行为，预测用户可能喜欢的物品：
```
输入：用户买过 [手机, 耳机, 充电器]
输出：推荐 [手机壳, 蓝牙音箱]
```

## 📁 代码文件详解

### 主要文件说明

#### `finetune.py` - 训练主脚本 ⭐⭐⭐
这是整个项目的核心，包含：
- **数据加载**：读取推荐数据
- **模型初始化**：设置LLaMA模型和LoRA
- **联邦训练**：多客户端轮流训练
- **模型聚合**：合并各客户端的学习成果
- **性能评估**：检查模型效果

#### `utils.py` - 工具函数集合 ⭐⭐
包含各种辅助函数：
- `split_dataset()`: 将用户分配到不同客户端
- `aggregate()`: 聚合多个客户端的模型
- `cluster_clients()`: 计算客户端相似度

#### `inference.py` - 推理脚本 ⭐
训练完成后，用这个脚本生成推荐结果

#### `train.sh` - 启动脚本
一键启动训练的便捷脚本

## 🔄 训练流程详解

### 第一步：数据准备
```python
# 1. 加载原始推荐数据
train_data = load_dataset("games_train.json")

# 2. 根据用户相似度分组
client_data = split_dataset(train_data, client_num=5)

# 3. 转换为模型可理解的格式
tokenized_data = tokenize(data)
```

### 第二步：模型初始化
```python
# 1. 加载预训练的LLaMA模型
model = LlamaForCausalLM.from_pretrained("llama-7b")

# 2. 添加LoRA适配器
model = get_peft_model(model, lora_config)

# 3. 为每个客户端创建模型副本
clients = [copy.deepcopy(model) for _ in range(5)]
```

### 第三步：联邦训练循环
```python
for epoch in range(num_epochs):
    # 1. 每个客户端本地训练
    for client_id in range(client_num):
        client_model = train_locally(client_data[client_id])
        save_model(client_model, f"client_{client_id}")
    
    # 2. 计算客户端相似度
    similarity_matrix = compute_similarity(all_client_models)
    
    # 3. 聚合模型参数
    for client_id in range(client_num):
        aggregated_weights = aggregate_weights(
            client_id, similarity_matrix, all_weights
        )
        update_client_model(client_id, aggregated_weights)
    
    # 4. 评估性能
    eval_results = evaluate_all_clients()
    
    # 5. 检查早停条件
    if no_improvement_for_patience_epochs:
        break
```

## 🛠️ 如何运行代码

### 环境准备
```bash
# 1. 安装Python依赖
pip install torch transformers peft datasets sklearn

# 2. 准备数据
# 确保 ./data/games/ 目录下有训练数据

# 3. 下载LLaMA模型
# 从HuggingFace下载或使用本地路径
```

### 运行训练
```bash
# 方法1：使用脚本（推荐）
cd BigRec_FELLRec
# 修改 train.sh 中的 base_model 路径
bash train.sh

# 方法2：直接运行Python
python finetune.py \
    --base_model "path/to/llama" \
    --client_num 5 \
    --num_epochs 10 \
    --learning_rate 1e-4
```

### 运行推理
```bash
# 训练完成后生成推荐
python inference.py \
    --base_model "path/to/llama" \
    --test_data_path "data/test.json"
```

## 📊 关键参数说明

### 联邦学习参数
- `client_num`: 客户端数量（建议3-10个）
- `round`: 每轮本地训练的epoch数（建议1-5）
- `alpha`: 聚合权重参数（0.5-1.0）
- `beta`: 权重调节参数（通常为1）

### LoRA参数
- `lora_r`: LoRA秩，控制新增参数量（4-64）
- `lora_alpha`: 缩放参数，影响LoRA重要性（8-32）
- `lora_dropout`: 防止过拟合（0.05-0.1）

### 训练参数
- `learning_rate`: 学习率（1e-5到1e-3）
- `batch_size`: 批次大小（16-128）
- `num_epochs`: 总训练轮数（10-100）

## 🔍 如何看懂训练日志

### 正常的训练日志
```
🚀 开始联邦学习训练，共 10 个epoch
📅 第 1/10 轮训练开始
🔧 正在准备客户端 0 的模型...
🏃‍♂️ 开始训练客户端 0，第 1 轮
✅ 客户端 0 训练完成，最终损失: 2.3456
🔄 开始第 1 轮的联邦模型聚合...
📊 计算客户端相似度矩阵...
⚖️ 计算客户端聚合权重...
🌍 第 1 轮全局评估损失: 2.1234
```

### 异常情况处理
- **内存不足**：减少`batch_size`或`client_num`
- **训练太慢**：检查GPU使用情况，考虑减少数据量
- **损失不下降**：调整学习率或检查数据质量

## 📈 实验设计建议

### 对比实验
1. **联邦 vs 集中式**：比较隐私保护效果
2. **不同客户端数量**：找到最优配置
3. **不同聚合策略**：测试算法改进

### 评估指标
- **推荐准确率**：Top-K准确率
- **训练效率**：收敛速度、通信成本
- **隐私保护**：数据泄露风险评估

## 🚨 常见问题解答

### Q: 为什么要用联邦学习？
A: 保护用户隐私，符合数据保护法规，同时能利用分布式数据训练更好的模型。

### Q: LoRA真的有效吗？
A: 是的！LoRA只用1-2%的参数就能达到全参数微调90%以上的效果。

### Q: 如何选择客户端数量？
A: 一般3-10个，太少学不到多样性，太多通信成本高。

### Q: 训练需要多长时间？
A: 取决于数据量和硬件，通常几小时到几天不等。

### Q: 如何改进模型效果？
A: 1) 调整超参数 2) 增加训练数据 3) 改进聚合策略 4) 使用更大的基础模型

## 🎓 进阶学习建议

### 理论基础
1. **联邦学习**：McMahan et al. "Communication-Efficient Learning"
2. **LoRA**：Hu et al. "LoRA: Low-Rank Adaptation"
3. **推荐系统**：经典协同过滤和深度学习方法

### 实践技能
1. **PyTorch**：深度学习框架
2. **Transformers**：HuggingFace库
3. **分布式训练**：多GPU、多机训练

### 研究方向
1. **隐私保护**：差分隐私、同态加密
2. **模型压缩**：知识蒸馏、剪枝
3. **个性化**：元学习、少样本学习

记住：科研是一个循序渐进的过程，先理解基础概念，再深入技术细节，最后尝试创新改进！🚀
