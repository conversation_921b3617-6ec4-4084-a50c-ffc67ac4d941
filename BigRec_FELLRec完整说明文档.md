# BigRec_FELLRec 完整说明文档

## 📁 项目概述

`BigRec_FELLRec` 是基于LLaMA大语言模型的联邦学习推荐系统实现。该项目将推荐任务转换为自然语言生成任务，利用联邦学习保护用户隐私，同时发挥大语言模型的强大表示能力。

### 🎯 核心价值
- **隐私保护**：用户数据不离开本地设备
- **智能推荐**：利用大语言模型的理解能力
- **高效训练**：使用LoRA技术大幅降低计算成本
- **可扩展性**：支持多客户端分布式训练

## 📂 文件夹结构详解

```
BigRec_FELLRec/
├── 📄 finetune.py          # 核心训练脚本 ⭐⭐⭐
├── 📄 utils.py             # 工具函数集合 ⭐⭐
├── 📄 inference.py         # 推理脚本 ⭐⭐
├── 📄 train.sh             # 训练启动脚本 ⭐
├── 📄 accelerate.yaml      # 分布式训练配置
├── 📄 requirements.txt     # Python依赖列表
├── 📄 training.log         # 训练日志文件
├── 📄 training_refer.log   # 参考训练日志
├── 📁 data/                # 数据目录
│   └── 📁 games/           # 游戏推荐数据集
├── 📁 model/               # 模型输出目录
└── 📁 __pycache__/         # Python缓存文件
```

## 📋 核心文件详细说明

### 1. finetune.py - 核心训练脚本 ⭐⭐⭐

**功能描述**：
这是整个项目的核心文件，实现了完整的联邦学习推荐系统训练流程。

**主要模块**：
- **数据处理模块**：加载、预处理和分词化推荐数据
- **模型初始化模块**：配置LLaMA模型和LoRA适配器
- **联邦训练模块**：多客户端本地训练和模型聚合
- **评估模块**：性能监控和早停机制
- **保存模块**：模型检查点和最佳模型管理

**关键技术**：
```python
# LoRA配置示例
config = LoraConfig(
    r=8,                    # LoRA秩
    lora_alpha=16,          # 缩放参数
    target_modules=["q_proj", "v_proj"],  # 目标模块
    lora_dropout=0.05,      # Dropout率
    task_type="CAUSAL_LM"   # 任务类型
)
```

**输入输出**：
- 输入：推荐数据集、模型配置参数
- 输出：训练好的客户端模型、训练日志

### 2. utils.py - 工具函数集合 ⭐⭐

**功能描述**：
包含联邦学习推荐系统的核心工具函数，支持数据分割、模型聚合等关键操作。

**主要函数**：

#### `split_dataset()`
```python
def split_dataset(train_data, n, val_data, test_data, pretrain_emb_path):
    """基于用户嵌入的智能数据分割"""
```
- **作用**：将用户数据分配到不同客户端
- **算法**：K-means聚类 + 用户嵌入
- **优势**：确保相似用户在同一客户端

#### `aggregate()`
```python
def aggregate(output_dir, device_map, client_num, save_name, base_model):
    """计算客户端相似度并准备聚合参数"""
```
- **作用**：计算客户端模型间的相似度矩阵
- **算法**：余弦相似度计算
- **输出**：相似度矩阵和聚合参数

#### `get_aggregate_lora_weight()`
```python
def get_aggregate_lora_weight(client_index, sim_matrix, accumulated_params, weight, beta):
    """基于相似度的LoRA权重聚合"""
```
- **作用**：根据相似度矩阵聚合LoRA权重
- **算法**：加权平均 + 动态权重调整

### 3. inference.py - 推理脚本 ⭐⭐

**功能描述**：
加载训练好的模型进行推荐推理，生成推荐结果。

**主要流程**：
1. 加载各客户端的最佳模型
2. 对测试数据进行批量推理
3. 生成推荐结果并保存

**使用示例**：
```bash
python inference.py \
    --base_model "path/to/llama" \
    --test_data_path "data/games/test.json"
```

### 4. train.sh - 训练启动脚本 ⭐

**功能描述**：
一键启动训练的便捷脚本，支持超参数网格搜索。

**配置示例**：
```bash
# 超参数配置
for seed in 1; do
    for lr in 1e-4; do
        for dropout in 0.05; do
            # 启动训练
            accelerate launch finetune.py \
                --base_model "path/to/llama" \
                --client_num 5 \
                --num_epochs 75
        done
    done
done
```

### 5. accelerate.yaml - 分布式训练配置

**功能描述**：
配置Accelerate框架的分布式训练参数。

**关键配置**：
```yaml
distributed_type: MULTI_GPU    # 多GPU训练
mixed_precision: fp16          # 半精度训练
num_processes: 4               # 进程数量
gpu_ids: 0,1,2,3              # GPU ID列表
```

## 🔄 数据流向和处理流程

### 整体数据流
```
原始推荐数据 → 数据预处理 → 客户端分割 → 本地训练 → 模型聚合 → 性能评估
```

### 详细处理流程

#### 阶段1：数据准备
```python
# 1. 加载原始数据
train_data = load_dataset("games_train.json")

# 2. 数据预处理和分词
tokenized_data = data.map(generate_and_tokenize_prompt)

# 3. 客户端分割
client_data = split_dataset(train_data, client_num=5)
```

#### 阶段2：模型初始化
```python
# 1. 加载基础模型
model = LlamaForCausalLM.from_pretrained(base_model)

# 2. 配置LoRA
model = get_peft_model(model, lora_config)

# 3. 客户端-服务器分割
model_server, model_client = split_client_server(model, k=20)
```

#### 阶段3：联邦训练
```python
for epoch in range(num_epochs):
    # 1. 客户端本地训练
    for client_id in range(client_num):
        local_model = train_locally(client_data[client_id])
    
    # 2. 计算相似度矩阵
    sim_matrix = compute_similarity(all_models)
    
    # 3. 模型聚合
    aggregated_weights = aggregate_models(sim_matrix)
    
    # 4. 性能评估
    eval_results = evaluate_models()
```

## 🔗 文件依赖关系

### 核心依赖图
```
finetune.py (主控制器)
    ├── utils.py (工具函数)
    │   ├── split_dataset()
    │   ├── aggregate()
    │   └── get_aggregate_lora_weight()
    ├── train.sh (启动脚本)
    ├── accelerate.yaml (配置文件)
    └── data/ (数据目录)

inference.py (推理脚本)
    ├── 训练好的模型
    └── 测试数据
```

### 运行时依赖
1. **finetune.py** 依赖 **utils.py** 的所有工具函数
2. **train.sh** 调用 **finetune.py** 并使用 **accelerate.yaml**
3. **inference.py** 加载 **finetune.py** 训练的模型
4. 所有脚本都依赖 **data/** 目录中的数据

## ⚙️ 配置文件详解

### accelerate.yaml 配置说明
```yaml
# 计算环境
compute_environment: LOCAL_MACHINE

# 分布式类型
distributed_type: MULTI_GPU     # 选项：NO, MULTI_GPU, MULTI_NODE

# 精度设置
mixed_precision: fp16           # 选项：no, fp16, bf16

# 硬件配置
num_machines: 1                 # 机器数量
num_processes: 4                # 进程数量（通常等于GPU数量）
gpu_ids: 0,1,2,3               # 使用的GPU ID

# 网络配置
main_process_port: 29503        # 主进程端口
```

### requirements.txt 依赖说明
```
torch>=2.1.1              # 深度学习框架
transformers>=4.30.0       # Transformer模型库
peft>=0.4.0               # 参数高效微调
accelerate>=0.20.0        # 分布式训练
datasets>=2.12.0          # 数据集处理
scikit-learn>=1.3.0       # 机器学习工具
numpy>=1.22.3             # 数值计算
fire>=0.5.0               # 命令行接口
```

## 🚀 使用指南

### 环境准备
```bash
# 1. 创建虚拟环境
conda create -n fellrec python=3.8
conda activate fellrec

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置加速器
accelerate config
```

### 数据准备
```bash
# 1. 准备推荐数据
mkdir -p data/games
# 将训练、验证、测试数据放入对应目录

# 2. 准备预训练嵌入
# 确保 pretrain_emb_path 指向正确的用户嵌入文件
```

### 训练流程
```bash
# 方法1：使用脚本（推荐）
cd BigRec_FELLRec
# 修改 train.sh 中的 base_model 路径
bash train.sh

# 方法2：直接运行
python finetune.py \
    --base_model "path/to/llama-7b" \
    --train_data_path '["./data/games/train_1024_user.json"]' \
    --val_data_path '["./data/games/valid_5000_user.json"]' \
    --client_num 5 \
    --num_epochs 10
```

### 推理流程
```bash
# 生成推荐结果
python inference.py \
    --base_model "path/to/llama-7b" \
    --test_data_path "data/games/test.json"
```

## 📊 监控和调试

### 训练监控
- **日志文件**：`training.log` 记录详细训练信息
- **控制台输出**：实时显示训练进度和性能指标
- **模型保存**：自动保存最佳模型和检查点

### 常见问题排查
1. **内存不足**：减少 `batch_size` 或 `client_num`
2. **训练缓慢**：检查GPU利用率，调整 `num_processes`
3. **收敛困难**：调整学习率或增加 `warmup_steps`
4. **精度下降**：检查数据质量或调整LoRA参数

## 🧪 实验设计指南

### 基础实验配置
```python
# 小规模测试配置
python finetune.py \
    --client_num 3 \
    --num_epochs 5 \
    --batch_size 32 \
    --sample 100

# 完整实验配置
python finetune.py \
    --client_num 5 \
    --num_epochs 75 \
    --batch_size 64 \
    --learning_rate 1e-4
```

### 对比实验建议
1. **联邦 vs 集中式**：比较隐私保护效果
2. **不同客户端数量**：测试3、5、10个客户端
3. **不同聚合策略**：测试算法改进效果
4. **LoRA参数影响**：测试不同rank值的效果

### 评估指标
- **推荐准确率**：Top-1, Top-5, Top-10准确率
- **训练效率**：收敛速度、通信轮数
- **资源消耗**：GPU内存、训练时间
- **隐私保护**：数据泄露风险评估

## 🔧 高级配置和优化

### 性能优化技巧
1. **内存优化**：
   ```python
   # 启用梯度检查点
   model.gradient_checkpointing_enable()

   # 使用8位量化
   load_in_8bit=True
   ```

2. **训练加速**：
   ```python
   # 启用编译优化
   if torch.__version__ >= "2":
       model = torch.compile(model)
   ```

3. **数据加载优化**：
   ```python
   # 并行数据加载
   dataloader = DataLoader(
       dataset,
       batch_size=batch_size,
       num_workers=4,
       pin_memory=True
   )
   ```

### 自定义扩展
1. **新增聚合策略**：
   ```python
   def custom_aggregate(client_weights, similarity_matrix):
       # 实现自定义聚合逻辑
       pass
   ```

2. **自定义评估指标**：
   ```python
   def compute_custom_metrics(predictions, labels):
       # 实现自定义评估指标
       pass
   ```

## 📈 结果分析和可视化

### 训练曲线分析
```python
import matplotlib.pyplot as plt

# 绘制损失曲线
def plot_training_curves(log_file):
    # 解析日志文件
    epochs, losses = parse_log(log_file)

    plt.figure(figsize=(10, 6))
    plt.plot(epochs, losses)
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training Loss Curve')
    plt.show()
```

### 客户端性能对比
```python
# 分析各客户端性能差异
def analyze_client_performance(eval_results):
    for i, result in enumerate(eval_results):
        print(f"客户端 {i}: 准确率 {result['accuracy']:.4f}")
```

## 🛡️ 安全和隐私考虑

### 隐私保护机制
1. **本地训练**：数据不离开客户端
2. **参数聚合**：只共享模型参数，不共享原始数据
3. **差分隐私**：可选添加噪声保护

### 安全最佳实践
1. **模型验证**：检查聚合后的模型完整性
2. **通信加密**：使用TLS加密客户端通信
3. **访问控制**：限制模型文件访问权限

## 🔍 故障排除指南

### 常见错误及解决方案

#### 1. CUDA内存不足
```
错误：RuntimeError: CUDA out of memory
解决：
- 减少 batch_size 或 micro_batch_size
- 减少 client_num
- 启用梯度累积
- 使用更小的模型
```

#### 2. 模型加载失败
```
错误：OSError: Can't load tokenizer
解决：
- 检查 base_model 路径是否正确
- 确保网络连接正常
- 使用本地模型路径
```

#### 3. 分布式训练失败
```
错误：RuntimeError: Address already in use
解决：
- 修改 accelerate.yaml 中的端口号
- 检查是否有其他进程占用端口
- 重启训练进程
```

#### 4. 数据加载错误
```
错误：FileNotFoundError: No such file
解决：
- 检查数据文件路径
- 确保数据格式正确
- 验证文件权限
```

## 📚 扩展阅读和参考资料

### 核心论文
1. **联邦学习**：
   - McMahan et al. "Communication-Efficient Learning of Deep Networks from Decentralized Data"
   - Li et al. "Federated Optimization in Heterogeneous Networks"

2. **LoRA技术**：
   - Hu et al. "LoRA: Low-Rank Adaptation of Large Language Models"
   - Dettmers et al. "QLoRA: Efficient Finetuning of Quantized LLMs"

3. **推荐系统**：
   - Kang et al. "Self-Attentive Sequential Recommendation"
   - Sun et al. "BERT4Rec: Sequential Recommendation with Bidirectional Encoder"

### 技术文档
- [HuggingFace Transformers](https://huggingface.co/docs/transformers)
- [PEFT Documentation](https://huggingface.co/docs/peft)
- [PyTorch Distributed](https://pytorch.org/tutorials/intermediate/ddp_tutorial.html)

### 开源项目
- [FederatedScope](https://github.com/alibaba/FederatedScope)
- [FedML](https://github.com/FedML-AI/FedML)
- [OpenFedLLM](https://github.com/rui-ye/OpenFedLLM)

这个完整的说明文档为您提供了使用 `BigRec_FELLRec` 进行联邦学习推荐系统研究的全方位指南！
