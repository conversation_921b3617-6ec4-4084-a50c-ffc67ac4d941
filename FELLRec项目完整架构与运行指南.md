# FELLRec联邦学习推荐系统 - 完整架构与运行指南

## 📖 文档概述

本文档详细分析FELLRec联邦学习推荐系统项目的完整架构和运行流程，为第一次接触联邦学习推荐系统的研究者提供全面的理解和操作指导。

## 🎯 项目简介

FELLRec (Federated Framework for LLM-based Recommendation) 是一个创新的联邦学习推荐系统，结合了：
- **大语言模型**：利用LLaMA的强大表示能力
- **联邦学习**：保护用户数据隐私
- **LoRA微调**：参数高效的模型适配
- **智能聚合**：基于相似度的模型聚合策略

## 📁 项目结构详细分析

### 整体项目架构图
```
FELLRec-main/
├── 🏢 BigRec_FELLRec/          # 基于LLaMA的主要实现（推荐使用）
├── 🏢 RecFormer_FELLRec/       # 基于RecFormer的备选实现
├── 📁 data/                    # 共享数据目录
├── 📄 README.md               # 项目说明文档
└── 📄 其他文档...
```

### 1. BigRec_FELLRec/ - 核心实现目录 ⭐⭐⭐

这是项目的**主要实现**，基于LLaMA大语言模型：

```
BigRec_FELLRec/
├── 🎯 finetune.py             # 联邦学习训练主脚本
├── 🔧 utils.py                # 核心工具函数集合
├── 🔮 inference.py            # 推理生成脚本
├── 🚀 train.sh                # 训练启动脚本
├── ⚙️ accelerate.yaml         # 分布式训练配置
├── 📋 requirements.txt        # Python依赖列表
├── 📊 training.log            # 训练日志文件
├── 📁 data/                   # 数据目录
│   └── 📁 games/              # 游戏推荐数据集
├── 📁 model/                  # 模型输出目录
└── 📁 __pycache__/            # Python缓存
```

#### 核心文件职责详解

**🎯 finetune.py - 联邦学习训练引擎**
```python
职责：联邦学习推荐系统的核心训练逻辑
功能：
- 数据预处理和客户端分配
- LoRA参数高效微调
- 多客户端本地训练
- 基于相似度的模型聚合
- 早停机制和性能评估

关键技术：
- 基于用户嵌入的智能数据分割
- 客户端-服务器模型分割
- 相似度驱动的模型聚合
- 动态权重调整机制
```

**🔧 utils.py - 联邦学习工具箱**
```python
职责：提供联邦学习的核心算法实现
关键函数：
- split_dataset(): 基于用户嵌入的智能数据分割
- aggregate(): 计算客户端相似度矩阵
- get_aggregate_lora_weight(): 聚合LoRA权重
- split_client_server(): 客户端-服务器模型分割

算法特点：
- K-means聚类用户分组
- 余弦相似度计算
- 加权平均聚合策略
```

**🔮 inference.py - 推理生成器**
```python
职责：使用训练好的模型生成推荐结果
功能：
- 加载各客户端的最佳模型
- 批量推理处理
- 推荐结果生成和保存

技术特点：
- 多客户端模型管理
- 批量处理优化
- 内存高效管理
```

**🚀 train.sh - 一键训练脚本**
```bash
职责：简化训练启动流程
功能：
- 超参数配置
- 网格搜索支持
- 自动化训练流程

配置项：
- 模型路径设置
- 联邦学习参数
- LoRA微调参数
```

### 2. RecFormer_FELLRec/ - 备选实现目录 ⭐⭐

基于RecFormer模型的实现，功能类似但架构不同：

```
RecFormer_FELLRec/
├── 🎯 finetune.py             # RecFormer训练脚本
├── 🔧 utils.py                # RecFormer工具函数
├── 📁 recformer/              # RecFormer模型定义
│   ├── modeling_recformer.py  # 模型架构定义
│   ├── configuration_recformer.py # 配置文件
│   └── tokenization_recformer.py  # 分词器
└── 📁 其他文件...

特点：
- 专门为推荐任务设计的Transformer架构
- 序列建模和物品表示学习
- 支持客户端-服务器分割
```

### 3. data/ - 数据管理中心 📊

```
data/
├── 📁 games/                  # 游戏推荐数据集
│   ├── 📄 train_1024_user.json    # 训练数据（1024用户）
│   ├── 📄 train_2048_user.json    # 训练数据（2048用户）
│   ├── 📄 valid_5000_user.json    # 验证数据（5000用户）
│   ├── 📄 test_user.json          # 测试数据
│   ├── 📄 meta_data.json          # 游戏元数据
│   ├── 📄 group_ckpt.pth.tar      # 预训练用户嵌入
│   ├── 🔍 evaluate.py             # 评估脚本
│   ├── 🔧 utils.py                # 评估工具
│   └── 📄 evaluate.sh             # 评估启动脚本
├── 📄 train_client_data.pkl       # 客户端训练数据
├── 📄 valid_client_data.pkl       # 客户端验证数据
├── 📄 test_client_data.pkl        # 客户端测试数据
└── 📁 其他数据集...

数据格式示例：
{
    "instruction": "根据用户的游戏历史，推荐下一个可能喜欢的游戏",
    "input": "用户玩过：《塞尔达传说：旷野之息》、《超级马里奥：奥德赛》",
    "output": "推荐：《马里奥卡丁车8豪华版》",
    "user": 12345
}
```

### 4. 配置文件详解

**⚙️ accelerate.yaml - 分布式训练配置**
```yaml
作用：配置多GPU分布式训练
关键设置：
- distributed_type: MULTI_GPU  # 多GPU模式
- mixed_precision: fp16        # 半精度训练
- num_processes: 4             # 进程数量
- gpu_ids: 0,1,2,3            # GPU设备列表

使用场景：
- 多GPU并行训练
- 混合精度优化
- 分布式数据并行
```

**📋 requirements.txt - 依赖管理**
```
作用：定义项目所需的Python包
核心依赖：
- torch>=2.1.1                # PyTorch深度学习框架
- transformers>=4.30.0         # HuggingFace模型库
- peft>=0.4.0                 # 参数高效微调库
- accelerate>=0.20.0          # 分布式训练库
- datasets>=2.12.0            # 数据集处理库
- scikit-learn>=1.3.0         # 机器学习工具库

安装命令：
pip install -r requirements.txt
```

## 🔄 完整运行流程和依赖关系

### 整体流程图
```
环境准备 → 数据准备 → 模型训练 → 推理生成 → 结果评估
    ↓         ↓         ↓         ↓         ↓
依赖安装   数据分割   联邦学习   批量推理   性能计算
    ↓         ↓         ↓         ↓         ↓
虚拟环境   PKL文件   LoRA权重   JSON结果   评估指标
```

### 详细运行步骤

#### 第一阶段：环境准备 🛠️ (约30分钟)

**步骤1：创建虚拟环境**
```bash
# 方法1：使用conda（推荐）
conda create -n fellrec python=3.8
conda activate fellrec

# 方法2：使用venv
python -m venv fellrec_env
source fellrec_env/bin/activate  # Linux/Mac
# fellrec_env\Scripts\activate   # Windows

# 验证环境
python --version  # 应显示Python 3.8.x
```

**步骤2：安装依赖**
```bash
cd BigRec_FELLRec
pip install -r requirements.txt

# 验证安装
python -c "import torch; print(f'PyTorch版本: {torch.__version__}')"
python -c "import transformers; print(f'Transformers版本: {transformers.__version__}')"
python -c "import peft; print(f'PEFT版本: {peft.__version__}')"

# 检查CUDA支持
python -c "import torch; print(f'CUDA可用: {torch.cuda.is_available()}')"
```

**步骤3：配置分布式训练**
```bash
# 首次配置accelerate
accelerate config

# 或直接使用提供的配置
cp accelerate.yaml ~/.cache/huggingface/accelerate/default_config.yaml

# 验证配置
accelerate env
```

#### 第二阶段：数据准备 📊 (约10分钟)

**步骤4：验证数据集**
```bash
# 检查数据目录结构
ls -la data/games/
# 应包含：train_*.json, valid_*.json, test_*.json, meta_data.json, group_ckpt.pth.tar

# 验证数据格式
python -c "
import json
with open('data/games/train_1024_user.json', 'r') as f:
    sample = json.loads(f.readline())
    print('数据格式正确:', sample.keys())
    print('示例数据:', sample)
"

# 检查数据统计
python -c "
import json
with open('data/games/train_1024_user.json', 'r') as f:
    data = [json.loads(line) for line in f]
    print(f'训练样本数量: {len(data)}')
    users = set(d['user'] for d in data)
    print(f'用户数量: {len(users)}')
"
```

**步骤5：准备基础模型**
```bash
# 方法1：下载LLaMA模型（需要申请权限）
git lfs install
git clone https://huggingface.co/meta-llama/Llama-2-7b-hf

# 方法2：使用本地模型路径
# 确保模型目录包含：config.json, pytorch_model.bin, tokenizer.json等

# 验证模型
python -c "
from transformers import LlamaTokenizer, LlamaForCausalLM
model_path = 'path/to/your/llama/model'
tokenizer = LlamaTokenizer.from_pretrained(model_path)
print('模型验证成功')
"
```

#### 第三阶段：联邦学习训练 🎯 (约2-8小时)

**步骤6：配置训练参数**
```bash
# 编辑训练脚本
vim train.sh

# 关键参数配置：
--base_model "path/to/your/llama/model" \  # 修改为你的模型路径
--client_num 5 \                           # 客户端数量
--num_epochs 10 \                          # 训练轮数
--learning_rate 1e-4 \                     # 学习率
--batch_size 64 \                          # 批次大小
--lora_r 8 \                              # LoRA秩
--alpha 0.7 \                             # 聚合权重参数
```

**步骤7：启动训练**
```bash
# 方法1：使用脚本（推荐）
bash train.sh

# 方法2：直接命令行
python finetune.py \
    --base_model "path/to/llama-7b" \
    --train_data_path '["./data/games/train_1024_user.json"]' \
    --val_data_path '["./data/games/valid_5000_user.json"]' \
    --test_data_path '["./data/games/test_user.json"]' \
    --output_dir ./model/games/experiment_1 \
    --client_num 5 \
    --num_epochs 10 \
    --learning_rate 1e-4

# 方法3：后台运行
nohup bash train.sh > training_output.log 2>&1 &
```

**训练过程监控**：
```bash
# 实时查看训练日志
tail -f training.log

# 监控GPU使用情况
nvidia-smi -l 1

# 查看训练进度
ls -la model/games/*/  # 查看模型保存情况

# 检查磁盘空间
df -h

# 查看进程状态
ps aux | grep python
```

**预期训练输出**：
```
🚀 开始FELLRec联邦学习推荐系统训练
📋 训练配置参数:
🤖 基础模型: /path/to/llama-7b
👥 客户端数量: 5
📅 第 1/10 轮联邦学习开始
🔧 正在准备客户端 1/5 的模型...
🏃‍♂️ 开始训练客户端 0，第 1 轮
✅ 客户端 0 训练完成，最终损失: 2.3456
🔄 开始第 1 轮的联邦模型聚合...
📊 计算客户端相似度矩阵...
⚖️ 计算客户端聚合权重...
🌍 第 1 轮全局评估损失: 2.1234
✅ 第 1 轮联邦学习完成
```

#### 第四阶段：推理生成 🔮 (约30分钟-2小时)

**步骤8：生成推荐结果**
```bash
# 启动推理
python inference.py \
    --base_model "path/to/llama-7b" \
    --load_8bit False \
    --batch_size 4

# 检查推理结果
ls -la games_client*.json

# 验证结果格式
python -c "
import json
with open('games_client0.json', 'r') as f:
    results = json.load(f)
    print(f'推理结果数量: {len(results)}')
    print('示例结果:', results[0])
"
```

**推理结果格式**：
```json
{
    "instruction": "根据用户历史推荐游戏",
    "input": "用户玩过：《塞尔达传说》、《超级马里奥》",
    "output": "推荐：《马里奥卡丁车》",
    "predict": "推荐：《马里奥卡丁车》、《动物森友会》",
    "user": 12345
}
```

#### 第五阶段：结果评估 📈 (约5-10分钟)

**步骤9：评估推荐性能**
```bash
# 进入评估目录
cd data/games

# 运行评估脚本
python evaluate.py --input_dir "../../"

# 或使用脚本
bash evaluate.sh

# 查看评估结果
cat evaluation_results.txt
```

**预期评估输出**：
```
📊 推荐性能评估结果：
- Top-1 准确率: 15.2%
- Top-5 准确率: 32.8%  
- Top-10 准确率: 45.6%
- Hit Rate@10: 0.456
- NDCG@10: 0.234

🎯 各客户端性能对比：
- 客户端 0: Top-10准确率 47.2%
- 客户端 1: Top-10准确率 44.8%
- 客户端 2: Top-10准确率 46.1%
- 客户端 3: Top-10准确率 43.5%
- 客户端 4: Top-10准确率 45.9%
```

## 🧠 整体运行逻辑详解

### 联邦学习推荐系统工作流程

#### 1. 数据分割阶段 📊
```
原始推荐数据 → 用户嵌入聚类 → 客户端数据分配
```

**详细过程**：
```python
# 步骤1：加载预训练用户嵌入
user_embeddings = torch.load('data/games/group_ckpt.pth.tar')

# 步骤2：K-means聚类分组用户
from sklearn.cluster import KMeans
kmeans = KMeans(n_clusters=client_num, random_state=42)
clusters = kmeans.fit_predict(user_embeddings)

# 步骤3：将相似用户分配到同一客户端
client_data = [[] for _ in range(client_num)]
for user_id, cluster_id in enumerate(clusters):
    user_data = get_user_data(user_id)
    client_data[cluster_id].extend(user_data)

# 步骤4：保存客户端数据
with open('data/train_client_data.pkl', 'wb') as f:
    pickle.dump(client_data, f)
```

**分割策略优势**：
- 相似用户聚集在同一客户端
- 提高本地训练效果
- 减少通信成本
- 保护用户隐私

#### 2. 联邦训练阶段 🎯
```
客户端本地训练 → 模型聚合 → 权重更新 → 性能评估
```

**训练循环详解**：
```python
for epoch in range(num_epochs):
    print(f"第 {epoch+1} 轮联邦学习开始")
    
    # 阶段1：各客户端本地训练
    train_losses = []
    for client_id in range(client_num):
        # 加载客户端模型
        model = load_client_model(client_id)
        
        # 本地训练
        trainer = Trainer(model=model, train_dataset=client_data[client_id])
        trainer.train()
        
        # 保存训练结果
        model.save_pretrained(f'model/client{client_id}')
        train_losses.append(trainer.state.log_history[-1]['train_loss'])
    
    # 阶段2：计算客户端相似度
    similarity_matrix = compute_client_similarity(client_models)
    
    # 阶段3：智能模型聚合
    aggregated_weights = []
    for client_id in range(client_num):
        # 计算聚合权重
        weight = compute_aggregation_weight(
            client_id, similarity_matrix, train_losses
        )
        
        # 聚合LoRA参数
        aggregated_weight = aggregate_lora_weights(
            client_id, similarity_matrix, all_weights, weight
        )
        
        # 更新客户端模型
        update_client_model(client_id, aggregated_weight)
    
    # 阶段4：性能评估和早停
    eval_results = []
    for client_id in range(client_num):
        eval_loss = evaluate_client(client_id, val_data[client_id])
        eval_results.append(eval_loss)
    
    # 计算全局性能
    global_eval_loss = weighted_average(eval_results, client_weights)
    
    # 早停检查
    if global_eval_loss < best_eval_loss:
        best_eval_loss = global_eval_loss
        early_stop_counter = 0
    else:
        early_stop_counter += 1
        
    if early_stop_counter >= patience:
        print("触发早停机制，训练结束")
        break
```

**核心算法**：

1. **相似度计算**：
```python
def compute_similarity(model1, model2):
    # 提取LoRA参数
    params1 = extract_lora_params(model1)
    params2 = extract_lora_params(model2)
    
    # 计算余弦相似度
    similarity = cosine_similarity(params1, params2)
    return similarity
```

2. **动态权重计算**：
```python
def compute_dynamic_weight(client_id, train_loss, epoch, alpha, beta):
    # 结合训练损失和epoch进度的动态权重
    weight = math.tanh(alpha / (train_loss ** (epoch + 1/beta)))
    return weight
```

3. **聚合策略**：
```python
def aggregate_weights(client_id, similarity_matrix, all_weights, weight):
    # 基于相似度的加权聚合
    aggregated = torch.zeros_like(all_weights[client_id])
    
    for other_id in range(len(all_weights)):
        similarity = similarity_matrix[client_id][other_id]
        contribution = similarity * weight * all_weights[other_id]
        aggregated += contribution
    
    return aggregated / sum(similarities)
```

#### 3. 推理生成阶段 🔮
```
加载最佳模型 → 批量推理 → 结果生成 → 文件保存
```

**推理流程详解**：
```python
def inference_pipeline():
    results = []
    
    for client_id in range(client_num):
        print(f"处理客户端 {client_id}")
        
        # 步骤1：加载客户端最佳模型
        model = LlamaForCausalLM.from_pretrained(base_model)
        model = PeftModel.from_pretrained(
            model, f'model/best_client{client_id}_model'
        )
        model.eval()
        
        # 步骤2：准备测试数据
        test_data = load_client_test_data(client_id)
        
        # 步骤3：批量推理
        predictions = []
        for batch in batch_iterator(test_data, batch_size=4):
            # 构建输入提示
            prompts = [generate_prompt(item) for item in batch]
            
            # 分词处理
            inputs = tokenizer(prompts, return_tensors="pt", padding=True)
            
            # 模型生成
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=128,
                    temperature=0.1,
                    do_sample=True
                )
            
            # 解码结果
            decoded = tokenizer.batch_decode(outputs, skip_special_tokens=True)
            predictions.extend(decoded)
        
        # 步骤4：后处理和保存
        for i, item in enumerate(test_data):
            item['predict'] = extract_recommendation(predictions[i])
            results.append(item)
        
        # 释放内存
        del model
        torch.cuda.empty_cache()
    
    # 保存最终结果
    with open('final_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    return results
```

### 数据流转图
```
原始JSON数据 → 预处理Token化 → 客户端PKL文件 → 本地LoRA训练 → 聚合权重更新 → 推理预测结果 → 评估性能指标
      ↓              ↓               ↓              ↓              ↓              ↓              ↓
  结构化推荐数据   数字序列表示    分布式数据存储   参数高效微调   智能模型聚合   个性化推荐生成   量化性能评估
```

**数据格式转换**：
```python
# 1. 原始数据格式
original_data = {
    "user": 12345,
    "history": ["game_A", "game_B", "game_C"],
    "target": "game_D"
}

# 2. 指令格式转换
instruction_data = {
    "instruction": "根据用户的游戏历史，推荐下一个可能喜欢的游戏",
    "input": "用户玩过：game_A、game_B、game_C",
    "output": "推荐：game_D",
    "user": 12345
}

# 3. 分词后格式
tokenized_data = {
    "input_ids": [1, 2, 3, ..., 1000],
    "attention_mask": [1, 1, 1, ..., 1],
    "labels": [1, 2, 3, ..., 1000]
}

# 4. 推理结果格式
inference_result = {
    "instruction": "根据用户的游戏历史，推荐下一个可能喜欢的游戏",
    "input": "用户玩过：game_A、game_B、game_C",
    "output": "推荐：game_D",
    "predict": "推荐：game_D、game_E",  # 模型预测
    "user": 12345
}
```

## 🛠️ 实际操作指导

### 新手完整操作流程

#### 准备阶段（约30分钟）
```bash
# 1. 克隆项目
git clone <project_url>
cd FELLRec-main/BigRec_FELLRec

# 2. 创建环境
conda create -n fellrec python=3.8
conda activate fellrec

# 3. 安装依赖
pip install -r requirements.txt

# 4. 验证安装
python -c "import torch, transformers, peft; print('环境配置成功')"

# 5. 下载或准备LLaMA模型
# 方法1：从HuggingFace下载（需要申请权限）
# 方法2：使用本地模型路径
```

#### 快速测试（约10分钟）
```bash
# 小规模测试，验证环境和代码
python finetune.py \
    --base_model "path/to/llama-7b" \
    --sample 50 \
    --num_epochs 1 \
    --client_num 2 \
    --batch_size 16

# 预期输出：成功完成1轮训练，无错误
```

#### 完整训练（约2-8小时）
```bash
# 1. 修改配置
vim train.sh
# 将 --base_model " " 改为你的模型路径

# 2. 启动训练
bash train.sh

# 3. 监控训练（另开终端）
tail -f training.log

# 4. 检查进度
ls -la model/games/*/
```

#### 推理和评估（约1小时）
```bash
# 1. 启动推理
python inference.py --base_model "path/to/llama-7b"

# 2. 检查结果
ls -la games_client*.json

# 3. 运行评估
cd data/games
python evaluate.py --input_dir "../../"
```

### 常见问题和解决方案

#### 1. 环境问题 🔧

**问题：CUDA版本不匹配**
```bash
# 错误信息：RuntimeError: CUDA version mismatch
# 解决方案：
# 1. 检查CUDA版本
nvidia-smi
nvcc --version

# 2. 安装匹配的PyTorch版本
pip install torch==2.1.1+cu118 -f https://download.pytorch.org/whl/torch_stable.html

# 3. 验证安装
python -c "import torch; print(torch.cuda.is_available())"
```

**问题：transformers版本过低**
```bash
# 错误信息：AttributeError: module 'transformers' has no attribute 'LlamaTokenizer'
# 解决方案：
pip install transformers>=4.30.0
pip install accelerate>=0.20.0
```

**问题：PEFT库缺失或版本不兼容**
```bash
# 错误信息：ImportError: No module named 'peft'
# 解决方案：
pip install peft>=0.4.0

# 如果版本冲突：
pip uninstall peft
pip install peft==0.4.0
```

#### 2. 内存问题 💾

**问题：GPU内存不足**
```bash
# 错误信息：RuntimeError: CUDA out of memory
# 解决方案：

# 方案1：减少批次大小
--batch_size 32          # 从64减少到32
--micro_batch_size 2     # 从4减少到2

# 方案2：减少客户端数量
--client_num 3           # 从5减少到3

# 方案3：启用8位量化
--load_8bit True

# 方案4：使用梯度检查点
# 在代码中添加：model.gradient_checkpointing_enable()

# 方案5：清理GPU缓存
python -c "import torch; torch.cuda.empty_cache()"
```

**问题：系统内存不足**
```bash
# 错误信息：MemoryError: Unable to allocate array
# 解决方案：

# 1. 减少数据加载量
--sample 500             # 限制训练样本数量

# 2. 启用数据流式加载
# 修改代码使用数据生成器而非一次性加载

# 3. 增加系统交换空间
sudo fallocate -l 8G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

#### 3. 数据问题 📊

**问题：数据文件不存在**
```bash
# 错误信息：FileNotFoundError: No such file or directory
# 解决方案：

# 1. 检查数据路径
ls -la data/games/
# 确保包含：train_*.json, valid_*.json, test_*.json

# 2. 检查文件权限
chmod 644 data/games/*.json

# 3. 验证文件完整性
wc -l data/games/train_1024_user.json
head -n 1 data/games/train_1024_user.json
```

**问题：数据格式错误**
```bash
# 错误信息：JSONDecodeError: Expecting value
# 解决方案：

# 1. 验证JSON格式
python -c "
import json
with open('data/games/train_1024_user.json', 'r') as f:
    for i, line in enumerate(f):
        try:
            json.loads(line)
        except json.JSONDecodeError as e:
            print(f'第{i+1}行格式错误: {e}')
            break
"

# 2. 检查必需字段
python -c "
import json
with open('data/games/train_1024_user.json', 'r') as f:
    sample = json.loads(f.readline())
    required_keys = ['instruction', 'input', 'output', 'user']
    missing = [k for k in required_keys if k not in sample]
    if missing:
        print(f'缺失字段: {missing}')
    else:
        print('数据格式正确')
"
```

**问题：预训练嵌入文件缺失**
```bash
# 错误信息：FileNotFoundError: group_ckpt.pth.tar
# 解决方案：

# 1. 检查文件是否存在
ls -la data/games/group_ckpt.pth.tar

# 2. 如果文件缺失，可以跳过基于嵌入的分割
# 修改utils.py中的split_dataset函数使用随机分割

# 3. 或者生成简单的用户嵌入
python -c "
import torch
import pickle
# 生成随机用户嵌入作为替代
num_users = 10000
embed_dim = 128
embeddings = torch.randn(num_users, embed_dim)
torch.save(embeddings, 'data/games/group_ckpt.pth.tar')
print('生成替代嵌入文件')
"
```

#### 4. 模型问题 🤖

**问题：模型加载失败**
```bash
# 错误信息：OSError: Can't load tokenizer
# 解决方案：

# 1. 检查模型路径
ls -la /path/to/llama-7b/
# 应包含：config.json, pytorch_model.bin, tokenizer.json等

# 2. 验证模型完整性
python -c "
from transformers import LlamaTokenizer, LlamaForCausalLM
model_path = '/path/to/llama-7b'
try:
    tokenizer = LlamaTokenizer.from_pretrained(model_path)
    print('分词器加载成功')
    model = LlamaForCausalLM.from_pretrained(model_path)
    print('模型加载成功')
except Exception as e:
    print(f'加载失败: {e}')
"

# 3. 检查网络连接（如果从HuggingFace下载）
ping huggingface.co

# 4. 使用镜像源
export HF_ENDPOINT=https://hf-mirror.com
```

**问题：LoRA配置错误**
```bash
# 错误信息：ValueError: Target modules not found
# 解决方案：

# 1. 检查目标模块名称
python -c "
from transformers import LlamaForCausalLM
model = LlamaForCausalLM.from_pretrained('path/to/llama-7b')
for name, module in model.named_modules():
    if 'proj' in name:
        print(name)
"

# 2. 修改LoRA目标模块
--lora_target_modules '[q_proj,v_proj,k_proj,o_proj]'

# 3. 验证LoRA配置
python -c "
from peft import LoraConfig
config = LoraConfig(
    r=8,
    lora_alpha=16,
    target_modules=['q_proj', 'v_proj'],
    lora_dropout=0.05,
    task_type='CAUSAL_LM'
)
print('LoRA配置正确')
"
```

#### 5. 训练问题 🎯

**问题：训练不收敛**
```bash
# 现象：损失不下降或震荡
# 解决方案：

# 1. 调整学习率
--learning_rate 5e-5     # 降低学习率
--learning_rate 2e-4     # 或提高学习率

# 2. 增加预热步数
--warmup_steps 100       # 增加预热

# 3. 调整LoRA参数
--lora_r 16              # 增加LoRA秩
--lora_alpha 32          # 调整缩放参数

# 4. 检查数据质量
python -c "
import json
with open('data/games/train_1024_user.json', 'r') as f:
    data = [json.loads(line) for line in f]
    # 检查数据分布
    instructions = [d['instruction'] for d in data]
    print(f'指令类型数量: {len(set(instructions))}')
    
    outputs = [d['output'] for d in data]
    avg_len = sum(len(o.split()) for o in outputs) / len(outputs)
    print(f'平均输出长度: {avg_len:.1f} 词')
"
```

**问题：训练速度过慢**
```bash
# 现象：每个epoch耗时过长
# 解决方案：

# 1. 增加批次大小（如果内存允许）
--batch_size 128         # 增加批次大小
--micro_batch_size 8     # 增加微批次大小

# 2. 启用模型编译（PyTorch 2.0+）
# 在代码中添加：
if torch.__version__ >= "2":
    model = torch.compile(model)

# 3. 使用更高效的优化器
--optim "adamw_torch"    # 使用PyTorch原生AdamW

# 4. 减少日志频率
--logging_steps 10       # 减少日志记录频率

# 5. 启用数据并行
export CUDA_VISIBLE_DEVICES=0,1,2,3
accelerate launch --multi_gpu finetune.py ...
```

#### 6. 推理问题 🔮

**问题：推理结果质量差**
```bash
# 现象：生成的推荐不合理
# 解决方案：

# 1. 调整生成参数
--temperature 0.1        # 降低温度，增加确定性
--top_p 0.9             # 调整核采样参数
--num_beams 4           # 使用束搜索

# 2. 检查模型是否正确加载
python -c "
import torch
from peft import PeftModel
from transformers import LlamaForCausalLM

# 验证模型加载
model = LlamaForCausalLM.from_pretrained('path/to/base/model')
model = PeftModel.from_pretrained(model, 'model/best_client0_model')
print('模型加载成功')

# 检查LoRA权重
lora_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
print(f'LoRA参数量: {lora_params:,}')
"

# 3. 验证输入格式
python -c "
def generate_prompt(data_point):
    return f'''Below is an instruction...
### Instruction:
{data_point['instruction']}
### Input:
{data_point['input']}
### Response:
'''

sample = {
    'instruction': '根据用户历史推荐游戏',
    'input': '用户玩过：游戏A、游戏B'
}
prompt = generate_prompt(sample)
print('提示格式:')
print(prompt)
"
```

**问题：推理速度过慢**
```bash
# 现象：推理耗时过长
# 解决方案：

# 1. 增加批次大小
--batch_size 8           # 增加推理批次大小

# 2. 启用8位量化
--load_8bit True

# 3. 使用更小的生成长度
--max_new_tokens 64      # 减少生成长度

# 4. 启用模型编译
if torch.__version__ >= "2":
    model = torch.compile(model)

# 5. 使用GPU推理
export CUDA_VISIBLE_DEVICES=0
```

### 调试技巧 🔍

#### 1. 日志分析
```bash
# 查看错误和警告
grep -E "ERROR|WARNING|Exception" training.log

# 查看训练进度
grep "epoch\|loss\|eval" training.log

# 查看内存使用
grep -i "memory\|cuda" training.log

# 实时监控
tail -f training.log | grep -E "客户端|损失|评估"
```

#### 2. 系统监控
```bash
# GPU使用监控
watch -n 1 nvidia-smi

# 内存使用监控
watch -n 1 free -h

# 磁盘空间监控
watch -n 5 df -h

# 进程监控
watch -n 2 "ps aux | grep python"

# 网络监控（如果需要下载模型）
iftop
```

#### 3. 分步调试
```bash
# 1. 测试环境
python -c "
import torch
import transformers
import peft
import accelerate
print('所有库导入成功')
print(f'PyTorch版本: {torch.__version__}')
print(f'CUDA可用: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'GPU数量: {torch.cuda.device_count()}')
    print(f'当前GPU: {torch.cuda.current_device()}')
"

# 2. 测试数据加载
python -c "
from utils import split_dataset
from datasets import load_dataset
print('开始测试数据加载...')
try:
    dataset = load_dataset('json', data_files='data/games/train_1024_user.json')
    print(f'数据加载成功，样本数: {len(dataset[\"train\"])}')
except Exception as e:
    print(f'数据加载失败: {e}')
"

# 3. 测试模型加载
python -c "
from transformers import LlamaForCausalLM, LlamaTokenizer
model_path = 'path/to/llama-7b'
print('开始测试模型加载...')
try:
    tokenizer = LlamaTokenizer.from_pretrained(model_path)
    print('分词器加载成功')
    model = LlamaForCausalLM.from_pretrained(model_path, torch_dtype=torch.float16)
    print('模型加载成功')
except Exception as e:
    print(f'模型加载失败: {e}')
"

# 4. 测试LoRA配置
python -c "
from peft import LoraConfig, get_peft_model
from transformers import LlamaForCausalLM
print('开始测试LoRA配置...')
try:
    model = LlamaForCausalLM.from_pretrained('path/to/llama-7b')
    config = LoraConfig(
        r=8, lora_alpha=16, target_modules=['q_proj', 'v_proj'],
        lora_dropout=0.05, task_type='CAUSAL_LM'
    )
    model = get_peft_model(model, config)
    print('LoRA配置成功')
    model.print_trainable_parameters()
except Exception as e:
    print(f'LoRA配置失败: {e}')
"

# 5. 测试训练流程
python -c "
# 最小化训练测试
import torch
from transformers import Trainer, TrainingArguments
print('开始测试训练流程...')
# 这里添加最小化的训练代码
"
```

#### 4. 性能分析
```bash
# 使用Python profiler
python -m cProfile -o profile_output.prof finetune.py --sample 100 --num_epochs 1

# 分析profile结果
python -c "
import pstats
p = pstats.Stats('profile_output.prof')
p.sort_stats('cumulative').print_stats(20)
"

# 使用line_profiler（需要安装）
pip install line_profiler
kernprof -l -v finetune.py

# GPU内存分析
python -c "
import torch
print('GPU内存使用情况:')
for i in range(torch.cuda.device_count()):
    print(f'GPU {i}:')
    print(f'  总内存: {torch.cuda.get_device_properties(i).total_memory / 1e9:.1f} GB')
    print(f'  已分配: {torch.cuda.memory_allocated(i) / 1e9:.1f} GB')
    print(f'  已缓存: {torch.cuda.memory_reserved(i) / 1e9:.1f} GB')
"
```

### 最佳实践建议 🌟

#### 1. 实验管理
```bash
# 创建实验目录结构
mkdir -p experiments/{exp001,exp002,exp003}
mkdir -p experiments/exp001/{logs,models,results}

# 记录实验配置
cat > experiments/exp001/config.yaml << EOF
experiment_name: "baseline_5clients"
base_model: "path/to/llama-7b"
client_num: 5
num_epochs: 10
learning_rate: 1e-4
lora_r: 8
lora_alpha: 16
batch_size: 64
notes: "基线实验，5个客户端"
EOF

# 运行实验
cd experiments/exp001
bash ../../train.sh > logs/training.log 2>&1
```

#### 2. 版本控制
```bash
# 记录代码版本
git log --oneline -1 > experiments/exp001/git_version.txt

# 记录环境信息
pip freeze > experiments/exp001/requirements_actual.txt
python --version > experiments/exp001/python_version.txt
nvidia-smi > experiments/exp001/gpu_info.txt
```

#### 3. 结果对比
```bash
# 创建结果对比脚本
cat > compare_experiments.py << EOF
import json
import pandas as pd

def compare_results():
    experiments = ['exp001', 'exp002', 'exp003']
    results = []
    
    for exp in experiments:
        try:
            with open(f'experiments/{exp}/results/evaluation.json', 'r') as f:
                data = json.load(f)
                results.append({
                    'experiment': exp,
                    'top1_acc': data['top1_accuracy'],
                    'top5_acc': data['top5_accuracy'],
                    'top10_acc': data['top10_accuracy'],
                    'training_time': data['training_time']
                })
        except FileNotFoundError:
            print(f'结果文件不存在: {exp}')
    
    df = pd.DataFrame(results)
    print(df.to_string(index=False))
    df.to_csv('experiment_comparison.csv', index=False)

if __name__ == '__main__':
    compare_results()
EOF

python compare_experiments.py
```

#### 4. 自动化脚本
```bash
# 创建自动化训练脚本
cat > auto_train.sh << EOF
#!/bin/bash

# 实验配置
EXPERIMENTS=(
    "exp001:5:1e-4:8"
    "exp002:3:1e-4:8"  
    "exp003:5:5e-5:16"
)

for exp_config in "\${EXPERIMENTS[@]}"; do
    IFS=':' read -r exp_name client_num lr lora_r <<< "\$exp_config"
    
    echo "开始实验: \$exp_name"
    mkdir -p experiments/\$exp_name/{logs,models,results}
    
    # 运行训练
    python finetune.py \\
        --base_model "path/to/llama-7b" \\
        --client_num \$client_num \\
        --learning_rate \$lr \\
        --lora_r \$lora_r \\
        --output_dir experiments/\$exp_name/models \\
        > experiments/\$exp_name/logs/training.log 2>&1
    
    # 运行推理
    python inference.py \\
        --base_model "path/to/llama-7b" \\
        > experiments/\$exp_name/logs/inference.log 2>&1
    
    # 运行评估
    cd data/games
    python evaluate.py --input_dir "../../experiments/\$exp_name" \\
        > ../../experiments/\$exp_name/logs/evaluation.log 2>&1
    cd ../..
    
    echo "完成实验: \$exp_name"
done

echo "所有实验完成"
EOF

chmod +x auto_train.sh
./auto_train.sh
```

## 📚 进阶主题和扩展

### 1. 超参数调优指南 🎛️

#### 联邦学习参数优化
```bash
# 客户端数量影响分析
for client_num in 3 5 7 10; do
    python finetune.py \
        --client_num $client_num \
        --output_dir "experiments/clients_$client_num" \
        --num_epochs 5
done

# 聚合参数调优
for alpha in 0.5 0.7 0.9; do
    for beta in 1 2 3; do
        python finetune.py \
            --alpha $alpha \
            --beta $beta \
            --output_dir "experiments/alpha_${alpha}_beta_${beta}"
    done
done
```

#### LoRA参数优化
```bash
# LoRA秩的影响
for lora_r in 4 8 16 32; do
    python finetune.py \
        --lora_r $lora_r \
        --output_dir "experiments/lora_r_$lora_r"
done

# 目标模块组合测试
declare -a modules=(
    "q_proj,v_proj"
    "q_proj,v_proj,k_proj"
    "q_proj,v_proj,o_proj"
    "q_proj,v_proj,gate_proj,up_proj,down_proj"
)

for module_set in "${modules[@]}"; do
    python finetune.py \
        --lora_target_modules "[$module_set]" \
        --output_dir "experiments/modules_${module_set//,/_}"
done
```

### 2. 性能优化技巧 ⚡

#### 内存优化策略
```python
# 1. 梯度检查点
model.gradient_checkpointing_enable()

# 2. 混合精度训练
from torch.cuda.amp import autocast, GradScaler
scaler = GradScaler()

with autocast():
    outputs = model(**inputs)
    loss = outputs.loss

scaler.scale(loss).backward()
scaler.step(optimizer)
scaler.update()

# 3. 动态批次大小
def get_optimal_batch_size():
    for batch_size in [64, 32, 16, 8, 4]:
        try:
            # 测试批次大小
            test_batch(batch_size)
            return batch_size
        except RuntimeError as e:
            if "out of memory" in str(e):
                torch.cuda.empty_cache()
                continue
            else:
                raise e
    return 1
```

#### 计算优化策略
```python
# 1. 模型编译（PyTorch 2.0+）
if torch.__version__ >= "2":
    model = torch.compile(model, mode="reduce-overhead")

# 2. 数据加载优化
from torch.utils.data import DataLoader
dataloader = DataLoader(
    dataset,
    batch_size=batch_size,
    num_workers=4,           # 多进程数据加载
    pin_memory=True,         # 固定内存
    prefetch_factor=2,       # 预取因子
    persistent_workers=True  # 持久化工作进程
)

# 3. 异步数据传输
for batch in dataloader:
    batch = {k: v.to(device, non_blocking=True) for k, v in batch.items()}
```

### 3. 分布式训练配置 🌐

#### 多GPU训练设置
```bash
# 1. 配置accelerate
accelerate config

# 配置示例：
# - 计算环境: LOCAL_MACHINE
# - 分布式类型: MULTI_GPU
# - GPU数量: 4
# - 混合精度: fp16

# 2. 启动多GPU训练
accelerate launch --multi_gpu --num_processes=4 finetune.py \
    --base_model "path/to/llama-7b" \
    --client_num 5

# 3. 手动配置DDP
export CUDA_VISIBLE_DEVICES=0,1,2,3
export WORLD_SIZE=4
export MASTER_ADDR=localhost
export MASTER_PORT=12355

for rank in 0 1 2 3; do
    export RANK=$rank
    python finetune.py --ddp &
done
wait
```

#### 多节点训练设置
```bash
# 主节点 (node 0)
export MASTER_ADDR=*************
export MASTER_PORT=12355
export WORLD_SIZE=8
export RANK=0
accelerate launch --multi_gpu --num_processes=4 finetune.py

# 从节点 (node 1)
export MASTER_ADDR=*************
export MASTER_PORT=12355
export WORLD_SIZE=8
export RANK=4
accelerate launch --multi_gpu --num_processes=4 finetune.py
```

### 4. 模型部署和服务化 🚀

#### 模型推理服务
```python
# inference_server.py
from flask import Flask, request, jsonify
import torch
from transformers import LlamaForCausalLM, LlamaTokenizer
from peft import PeftModel

app = Flask(__name__)

class RecommendationService:
    def __init__(self, base_model_path, client_models_path):
        self.tokenizer = LlamaTokenizer.from_pretrained(base_model_path)
        self.models = {}

        # 加载所有客户端模型
        for client_id in range(5):
            model = LlamaForCausalLM.from_pretrained(base_model_path)
            model = PeftModel.from_pretrained(
                model, f"{client_models_path}/best_client{client_id}_model"
            )
            model.eval()
            self.models[client_id] = model

    def recommend(self, user_history, client_id=0):
        # 构建输入提示
        prompt = f"""根据用户的游戏历史，推荐下一个可能喜欢的游戏

        ### 用户历史:
        {user_history}

        ### 推荐:
        """

        # 分词和生成
        inputs = self.tokenizer(prompt, return_tensors="pt")
        with torch.no_grad():
            outputs = self.models[client_id].generate(
                **inputs,
                max_new_tokens=50,
                temperature=0.1,
                do_sample=True
            )

        # 解码结果
        response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        recommendation = response.split("### 推荐:")[-1].strip()

        return recommendation

# 初始化服务
service = RecommendationService(
    base_model_path="path/to/llama-7b",
    client_models_path="model/games"
)

@app.route('/recommend', methods=['POST'])
def recommend():
    data = request.json
    user_history = data.get('history', '')
    client_id = data.get('client_id', 0)

    try:
        recommendation = service.recommend(user_history, client_id)
        return jsonify({
            'status': 'success',
            'recommendation': recommendation
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
```

#### Docker部署
```dockerfile
# Dockerfile
FROM nvidia/cuda:11.8-devel-ubuntu20.04

# 安装Python和依赖
RUN apt-get update && apt-get install -y \
    python3 python3-pip git \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制项目文件
COPY requirements.txt .
RUN pip3 install -r requirements.txt

COPY . .

# 暴露端口
EXPOSE 5000

# 启动服务
CMD ["python3", "inference_server.py"]
```

```bash
# 构建和运行Docker容器
docker build -t fellrec-service .
docker run --gpus all -p 5000:5000 -v /path/to/models:/app/models fellrec-service
```

### 5. 实验分析和可视化 📊

#### 训练过程可视化
```python
# visualize_training.py
import matplotlib.pyplot as plt
import json
import pandas as pd

def plot_training_curves():
    # 解析训练日志
    losses = []
    eval_losses = []
    epochs = []

    with open('training.log', 'r') as f:
        for line in f:
            if '训练完成，最终损失' in line:
                loss = float(line.split('损失: ')[1].split()[0])
                losses.append(loss)
            elif '全局评估损失' in line:
                eval_loss = float(line.split('损失: ')[1].split()[0])
                eval_losses.append(eval_loss)
                epochs.append(len(eval_losses))

    # 绘制训练曲线
    plt.figure(figsize=(12, 5))

    plt.subplot(1, 2, 1)
    plt.plot(epochs, eval_losses, 'b-', label='验证损失')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('训练过程 - 验证损失')
    plt.legend()
    plt.grid(True)

    plt.subplot(1, 2, 2)
    # 按客户端分组显示训练损失
    client_losses = [losses[i:i+5] for i in range(0, len(losses), 5)]
    for i, client_loss in enumerate(client_losses):
        plt.plot(range(len(client_loss)), client_loss, label=f'客户端 {i}')
    plt.xlabel('客户端ID')
    plt.ylabel('训练损失')
    plt.title('各客户端训练损失')
    plt.legend()
    plt.grid(True)

    plt.tight_layout()
    plt.savefig('training_curves.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_client_performance():
    # 客户端性能对比
    client_results = []
    for i in range(5):
        with open(f'games_client{i}.json', 'r') as f:
            data = json.load(f)
            # 计算准确率
            correct = sum(1 for item in data if item['output'] in item['predict'])
            accuracy = correct / len(data)
            client_results.append({
                'client_id': i,
                'accuracy': accuracy,
                'samples': len(data)
            })

    df = pd.DataFrame(client_results)

    plt.figure(figsize=(10, 6))
    plt.bar(df['client_id'], df['accuracy'])
    plt.xlabel('客户端ID')
    plt.ylabel('准确率')
    plt.title('各客户端推荐准确率对比')
    plt.grid(True, alpha=0.3)

    # 添加数值标签
    for i, v in enumerate(df['accuracy']):
        plt.text(i, v + 0.01, f'{v:.3f}', ha='center')

    plt.savefig('client_performance.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == '__main__':
    plot_training_curves()
    plot_client_performance()
```

#### 联邦学习效果分析
```python
# federated_analysis.py
import numpy as np
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE
import torch

def analyze_client_similarity():
    """分析客户端模型相似度"""
    # 加载客户端LoRA权重
    client_weights = []
    for i in range(5):
        weights = torch.load(f'model/games/best_client{i}_model/adapter_model.bin')
        # 展平权重向量
        flattened = torch.cat([w.flatten() for w in weights.values()])
        client_weights.append(flattened.numpy())

    # 计算相似度矩阵
    similarity_matrix = np.zeros((5, 5))
    for i in range(5):
        for j in range(5):
            similarity = np.dot(client_weights[i], client_weights[j]) / (
                np.linalg.norm(client_weights[i]) * np.linalg.norm(client_weights[j])
            )
            similarity_matrix[i][j] = similarity

    # 可视化相似度矩阵
    plt.figure(figsize=(8, 6))
    plt.imshow(similarity_matrix, cmap='coolwarm', vmin=-1, vmax=1)
    plt.colorbar(label='余弦相似度')
    plt.title('客户端模型相似度矩阵')
    plt.xlabel('客户端ID')
    plt.ylabel('客户端ID')

    # 添加数值标签
    for i in range(5):
        for j in range(5):
            plt.text(j, i, f'{similarity_matrix[i][j]:.3f}',
                    ha='center', va='center')

    plt.savefig('client_similarity.png', dpi=300, bbox_inches='tight')
    plt.show()

    return similarity_matrix

def visualize_user_distribution():
    """可视化用户分布"""
    # 加载用户嵌入
    embeddings = torch.load('data/games/group_ckpt.pth.tar')

    # 使用t-SNE降维
    tsne = TSNE(n_components=2, random_state=42)
    embeddings_2d = tsne.fit_transform(embeddings[:1000])  # 取前1000个用户

    # 加载客户端分配
    with open('data/train_client_data.pkl', 'rb') as f:
        client_data = pickle.load(f)

    # 获取用户-客户端映射
    user_client_map = {}
    for client_id, data in enumerate(client_data):
        for item in data:
            user_client_map[item['user']] = client_id

    # 绘制用户分布
    plt.figure(figsize=(10, 8))
    colors = ['red', 'blue', 'green', 'orange', 'purple']

    for client_id in range(5):
        client_users = [i for i, user_id in enumerate(range(1000))
                       if user_client_map.get(user_id, 0) == client_id]
        if client_users:
            plt.scatter(embeddings_2d[client_users, 0],
                       embeddings_2d[client_users, 1],
                       c=colors[client_id],
                       label=f'客户端 {client_id}',
                       alpha=0.6)

    plt.xlabel('t-SNE维度1')
    plt.ylabel('t-SNE维度2')
    plt.title('用户嵌入分布（按客户端着色）')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig('user_distribution.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == '__main__':
    similarity_matrix = analyze_client_similarity()
    visualize_user_distribution()
```

### 6. 故障排除和维护 🔧

#### 自动化健康检查
```bash
# health_check.sh
#!/bin/bash

echo "=== FELLRec系统健康检查 ==="

# 1. 检查Python环境
echo "1. 检查Python环境..."
python --version
pip list | grep -E "torch|transformers|peft|accelerate"

# 2. 检查GPU状态
echo "2. 检查GPU状态..."
nvidia-smi --query-gpu=name,memory.total,memory.used --format=csv

# 3. 检查数据文件
echo "3. 检查数据文件..."
for file in "data/games/train_1024_user.json" "data/games/valid_5000_user.json" "data/games/test_user.json"; do
    if [ -f "$file" ]; then
        echo "✓ $file 存在"
        wc -l "$file"
    else
        echo "✗ $file 缺失"
    fi
done

# 4. 检查模型文件
echo "4. 检查模型文件..."
if [ -d "model/games" ]; then
    echo "✓ 模型目录存在"
    ls -la model/games/
else
    echo "✗ 模型目录不存在"
fi

# 5. 检查磁盘空间
echo "5. 检查磁盘空间..."
df -h | grep -E "/$|/home|/tmp"

# 6. 检查进程状态
echo "6. 检查训练进程..."
ps aux | grep -E "python.*finetune|python.*inference" | grep -v grep

echo "=== 健康检查完成 ==="
```

#### 自动备份脚本
```bash
# backup.sh
#!/bin/bash

BACKUP_DIR="/backup/fellrec/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "开始备份到: $BACKUP_DIR"

# 备份模型文件
if [ -d "model" ]; then
    echo "备份模型文件..."
    cp -r model "$BACKUP_DIR/"
fi

# 备份配置文件
echo "备份配置文件..."
cp train.sh accelerate.yaml requirements.txt "$BACKUP_DIR/"

# 备份日志文件
echo "备份日志文件..."
cp *.log "$BACKUP_DIR/" 2>/dev/null || true

# 备份实验结果
if [ -d "experiments" ]; then
    echo "备份实验结果..."
    cp -r experiments "$BACKUP_DIR/"
fi

# 创建备份信息文件
cat > "$BACKUP_DIR/backup_info.txt" << EOF
备份时间: $(date)
Git版本: $(git log --oneline -1 2>/dev/null || echo "未知")
Python版本: $(python --version)
PyTorch版本: $(python -c "import torch; print(torch.__version__)" 2>/dev/null || echo "未知")
EOF

echo "备份完成: $BACKUP_DIR"

# 清理旧备份（保留最近7天）
find /backup/fellrec -type d -mtime +7 -exec rm -rf {} + 2>/dev/null || true
```

#### 监控和告警
```python
# monitor.py
import time
import psutil
import GPUtil
import smtplib
from email.mime.text import MIMEText
import logging

class SystemMonitor:
    def __init__(self, email_config=None):
        self.email_config = email_config
        self.setup_logging()

    def setup_logging(self):
        logging.basicConfig(
            filename='monitor.log',
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )

    def check_gpu_memory(self, threshold=0.9):
        """检查GPU内存使用率"""
        try:
            gpus = GPUtil.getGPUs()
            for gpu in gpus:
                usage = gpu.memoryUsed / gpu.memoryTotal
                if usage > threshold:
                    message = f"GPU {gpu.id} 内存使用率过高: {usage:.1%}"
                    self.send_alert(message)
                    logging.warning(message)
        except Exception as e:
            logging.error(f"GPU检查失败: {e}")

    def check_disk_space(self, threshold=0.9):
        """检查磁盘空间"""
        disk_usage = psutil.disk_usage('/')
        usage = disk_usage.used / disk_usage.total
        if usage > threshold:
            message = f"磁盘空间不足: {usage:.1%}"
            self.send_alert(message)
            logging.warning(message)

    def check_training_process(self):
        """检查训练进程状态"""
        training_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if 'python' in proc.info['name'] and 'finetune.py' in ' '.join(proc.info['cmdline']):
                    training_processes.append(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass

        if not training_processes:
            message = "未发现训练进程运行"
            logging.info(message)
        else:
            for proc in training_processes:
                cpu_percent = proc.cpu_percent()
                memory_percent = proc.memory_percent()
                message = f"训练进程 {proc.pid}: CPU {cpu_percent:.1f}%, 内存 {memory_percent:.1f}%"
                logging.info(message)

    def send_alert(self, message):
        """发送告警邮件"""
        if not self.email_config:
            return

        try:
            msg = MIMEText(message)
            msg['Subject'] = 'FELLRec系统告警'
            msg['From'] = self.email_config['from']
            msg['To'] = self.email_config['to']

            server = smtplib.SMTP(self.email_config['smtp_server'])
            server.starttls()
            server.login(self.email_config['username'], self.email_config['password'])
            server.send_message(msg)
            server.quit()

            logging.info(f"告警邮件已发送: {message}")
        except Exception as e:
            logging.error(f"发送告警邮件失败: {e}")

    def run_monitoring(self, interval=300):
        """运行监控循环"""
        logging.info("开始系统监控")
        while True:
            try:
                self.check_gpu_memory()
                self.check_disk_space()
                self.check_training_process()
                time.sleep(interval)
            except KeyboardInterrupt:
                logging.info("监控停止")
                break
            except Exception as e:
                logging.error(f"监控异常: {e}")
                time.sleep(60)

if __name__ == '__main__':
    # 配置邮件告警（可选）
    email_config = {
        'smtp_server': 'smtp.gmail.com',
        'username': '<EMAIL>',
        'password': 'your_password',
        'from': '<EMAIL>',
        'to': '<EMAIL>'
    }

    monitor = SystemMonitor(email_config)
    monitor.run_monitoring(interval=300)  # 每5分钟检查一次
```

## 📖 总结

这个完整的FELLRec项目架构与运行指南涵盖了：

### ✅ 核心内容
1. **项目结构分析** - 详细的文件和目录说明
2. **运行流程指导** - 从环境配置到结果评估的完整步骤
3. **技术原理解释** - 联邦学习和LoRA微调的工作机制
4. **实际操作指导** - 针对新手的具体命令和示例

### ✅ 进阶内容
1. **超参数调优** - 系统性的参数优化策略
2. **性能优化** - 内存和计算效率提升技巧
3. **分布式训练** - 多GPU和多节点配置
4. **模型部署** - 服务化和Docker部署方案

### ✅ 运维支持
1. **故障排除** - 常见问题的诊断和解决方案
2. **监控告警** - 自动化的系统健康检查
3. **实验管理** - 版本控制和结果对比
4. **可视化分析** - 训练过程和结果的图表展示

这个文档将帮助您：
- 🎯 **快速上手**：按照步骤指导完成首次运行
- 🔧 **深入理解**：掌握联邦学习推荐系统的技术原理
- 🚀 **优化提升**：通过调优和优化获得更好的性能
- 🛡️ **稳定运行**：建立完善的监控和维护机制

无论您是联邦学习的新手还是希望深入研究推荐系统，这个指南都将为您提供全面的支持！🚀
