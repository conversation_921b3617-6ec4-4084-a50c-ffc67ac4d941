"""
FELLRec (Federated Framework for LLM-based Recommendation) - BigRec版本
基于LLaMA大语言模型的联邦学习推荐系统微调脚本

==============================================================================
文件功能详细描述：
==============================================================================
本脚本是FELLRec项目的核心训练模块，实现了一个完整的联邦学习推荐系统。
该系统结合了大语言模型的强大表示能力和联邦学习的隐私保护特性，
为推荐系统领域提供了创新的解决方案。

主要功能模块：
1. 数据预处理与客户端分配 - 基于用户嵌入的智能数据分割
2. 模型初始化与配置 - LLaMA模型加载和LoRA适配器设置
3. 联邦训练循环 - 多客户端本地训练和全局模型聚合
4. 性能评估与优化 - 实时监控和早停机制
5. 模型保存与管理 - 最佳模型选择和检查点管理

==============================================================================
主要技术特点和创新点：
==============================================================================
1. 【联邦学习架构】
   - 多客户端分布式训练，保护用户数据隐私
   - 基于相似度矩阵的智能模型聚合策略
   - 动态权重调整机制，提高聚合效果

2. 【参数高效微调】
   - 使用LoRA (Low-Rank Adaptation) 技术
   - 仅训练1-2%的参数，大幅降低计算成本
   - 支持8位量化，进一步节省显存

3. 【智能数据分割】
   - 基于预训练用户嵌入的K-means聚类
   - 确保相似用户分配到同一客户端
   - 提高联邦学习的收敛速度和效果

4. 【推荐系统优化】
   - 将推荐任务转换为自然语言生成任务
   - 利用大语言模型的上下文理解能力
   - 支持可解释的推荐结果生成

==============================================================================
使用方法和参数说明：
==============================================================================
命令行使用方式：
python finetune.py --base_model "path/to/llama" --client_num 5 --num_epochs 10

主要参数分类：
【模型参数】
- base_model: LLaMA基础模型路径 (必需)
- output_dir: 模型输出目录
- pretrain_emb_path: 预训练用户嵌入路径

【联邦学习参数】
- client_num: 客户端数量 (默认3)
- round: 每轮本地训练epoch数 (默认1)
- alpha: 聚合权重参数 (默认0.7)
- beta: 权重调节参数 (默认1)
- k: 客户端-服务器分割层数 (默认20)

【LoRA参数】
- lora_r: LoRA秩 (默认8)
- lora_alpha: LoRA缩放参数 (默认16)
- lora_dropout: LoRA dropout率 (默认0.05)

【训练参数】
- learning_rate: 学习率 (默认3e-4)
- batch_size: 总批次大小 (默认128)
- micro_batch_size: 微批次大小 (默认4)
- num_epochs: 训练轮数 (默认3)
- patience: 早停耐心值 (默认5)

==============================================================================
作者信息和版本信息：
==============================================================================
项目名称: FELLRec (Federated Framework for LLM-based Recommendation)
版本: v1.0.0
开发团队: [请填入您的团队信息]
联系邮箱: [请填入联系邮箱]
开发时间: 2024年
许可证: MIT License

技术栈:
- PyTorch 2.1.1+
- Transformers 4.30+
- PEFT (Parameter-Efficient Fine-Tuning)
- Accelerate (分布式训练)
- scikit-learn (聚类算法)

引用信息:
如果您在研究中使用了本代码，请引用相关论文：
[请填入论文引用信息]

==============================================================================
"""

# ==================== 环境配置和库导入 ====================
# 这部分代码配置运行环境并导入所需的库，是联邦学习推荐系统的基础设置

import os
# 【环境变量设置】- 解决多线程冲突和网络连接问题
# 这些设置对于稳定运行联邦学习训练非常重要
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'  # 解决OpenMP库重复加载问题，避免训练崩溃
os.environ["CURL_CA_BUNDLE"] = ""  # 忽略SSL证书验证，解决网络下载问题
os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"  # 使用HuggingFace镜像源，提高下载速度

import ssl
ssl._create_default_https_context = ssl._create_unverified_context  # 跳过SSL验证（临时方案）

# ==================== 核心库导入 ====================
# 这些是实现联邦学习推荐系统的核心库

import sys  # 系统相关功能
from typing import List  # 类型提示，用于函数参数声明
import numpy as np  # 数值计算库，用于矩阵运算和数据处理
import fire  # 命令行接口库，将Python函数转换为命令行工具
import torch  # PyTorch深度学习框架，整个系统的基础
import transformers  # HuggingFace Transformers库，提供预训练模型

# 【数据处理相关】
from datasets import load_dataset, concatenate_datasets  # 数据集加载和合并工具

# 【模型和训练相关】
from transformers import EarlyStoppingCallback, LlamaForCausalLM, LlamaTokenizer  # LLaMA模型组件
from transformers import Trainer, TrainingArguments  # 训练器和训练参数配置
from copy import deepcopy  # 深拷贝，用于模型复制

# ==================== PEFT (参数高效微调) 相关导入 ====================
# PEFT是实现LoRA微调的核心库，这是联邦学习中节省计算资源的关键技术
from peft import (  # noqa: E402
    LoraConfig,  # LoRA配置类，定义LoRA的超参数
    get_peft_model,  # 将普通模型转换为PEFT模型
    get_peft_model_state_dict,  # 获取PEFT模型的状态字典（只包含LoRA参数）
    prepare_model_for_int8_training,  # 为8位量化训练准备模型，节省显存
    set_peft_model_state_dict,  # 设置PEFT模型的状态字典，用于加载训练好的LoRA权重
)

# 【其他必要库】
import pickle  # Python对象序列化，用于保存和加载客户端数据
import time  # 时间相关功能，用于训练时间统计
from peft import PeftModel  # PEFT模型类，用于加载已训练的LoRA模型
import torch.distributed as dist  # PyTorch分布式训练支持
import math  # 数学函数库

# ==================== 自定义工具函数导入 ====================
# 这些是专门为联邦学习推荐系统设计的工具函数
from utils import split_dataset, aggregate, LoggingCallback, get_aggregate_lora_weight, merge_models, split_client_server
# split_dataset: 【核心】基于用户嵌入的数据集分割函数
# aggregate: 【核心】客户端模型聚合函数
# LoggingCallback: 自定义日志回调，记录训练过程
# get_aggregate_lora_weight: 【核心】获取聚合后的LoRA权重
# merge_models: 模型合并函数
# split_client_server: 【核心】客户端-服务器模型分割函数

# 【最终环境设置】
os.environ['LD_LIBRARY_PATH'] = ''  # 清空动态库路径，避免冲突
import logging  # 日志记录系统
# dist.init_process_group(backend='nccl', init_method='env://')  # 分布式初始化（已注释，按需启用）

os.environ['KMP_DUPLICATE_LIB_OK']='TRUE'  # 再次确保OpenMP设置正确
# ==================== 联邦学习权重计算函数 ====================
def softmax_with_temperature(x, temperature=0.05):
    """
    带温度参数的softmax函数 - 联邦学习中客户端权重计算的核心函数

    【功能说明】
    这个函数用于计算联邦学习中各客户端的聚合权重。温度参数控制权重分布的平滑程度：
    - 低温度(如0.05)：权重分布更尖锐，性能好的客户端权重更大
    - 高温度(如1.0)：权重分布更平滑，各客户端权重相对均匀

    【在联邦学习中的作用】
    1. 输入客户端的训练损失列表
    2. 损失越小的客户端，在聚合时权重越大
    3. 这样可以让表现好的客户端对全局模型影响更大

    参数:
        x (list): 客户端损失值列表，例如 [2.1, 1.8, 2.3, 1.9]
        temperature (float): 温度参数，控制分布尖锐程度，默认0.05

    返回:
        numpy.ndarray: 归一化的权重分布，和为1，例如 [0.2, 0.4, 0.1, 0.3]
    """
    x = np.array(x) / temperature  # 将损失除以温度，温度越小分布越尖锐
    e_x = np.exp(x - np.max(x))    # 数值稳定的指数计算，减去最大值防止溢出
    return e_x / e_x.sum()         # 归一化得到概率分布

# ==================== 联邦学习训练主函数 ====================
def train(
    # ========== 模型和数据参数 ==========
    base_model: str = "",  # 【必需】LLaMA基础模型路径，例如 "meta-llama/Llama-2-7b-hf"
    train_data_path: List[str] = [""],  # 训练数据文件路径列表
    val_data_path: List[str] = [""],    # 验证数据文件路径列表
    test_data_path: List[str] = [""],   # 测试数据文件路径列表
    output_dir: str = "./lora-alpaca",  # 模型输出目录，保存训练好的LoRA权重
    pretrain_emb_path: str = "../data/games/group_ckpt.pth.tar",  # 预训练用户嵌入文件路径
    sample: int = -1,  # 训练样本数量限制，-1表示使用全部数据
    seed: int = 0,     # 随机种子，确保实验可重复

    # ========== 训练超参数 ==========
    batch_size: int = 128,        # 总批次大小，影响训练稳定性和速度
    micro_batch_size: int = 4,    # 微批次大小，用于梯度累积，实际显存占用由此决定
    num_epochs: int = 3,          # 联邦学习总轮数，每轮包含多个客户端本地训练
    learning_rate: float = 3e-4,  # 学习率，控制参数更新步长
    cutoff_len: int = 512,        # 输入序列最大长度，超过会被截断

    # ========== LoRA (低秩适应) 超参数 ==========
    # LoRA是参数高效微调技术，只训练少量参数就能达到全参数微调的效果
    lora_r: int = 8,              # LoRA秩，控制新增参数量，越大效果越好但参数越多
    lora_alpha: int = 16,         # LoRA缩放参数，控制LoRA权重的重要性
    lora_dropout: float = 0.05,   # LoRA层的dropout率，防止过拟合
    lora_target_modules: List[str] = [  # LoRA应用的目标模块
        "q_proj",  # 查询投影层，注意力机制的核心组件
        "v_proj",  # 值投影层，注意力机制的核心组件
    ],

    # ========== 大语言模型训练参数 ==========
    train_on_inputs: bool = True,   # 是否在输入部分计算损失，True表示整个序列都参与训练
    group_by_length: bool = False,  # 是否按序列长度分组，可以加速但可能影响训练曲线

    # ========== Weights & Biases 监控参数 ==========
    wandb_project: str = "",      # WandB项目名，用于实验跟踪
    wandb_run_name: str = "",     # WandB运行名
    wandb_watch: str = "",        # WandB监控级别：false | gradients | all
    wandb_log_model: str = "",    # 是否记录模型：false | true
    resume_from_checkpoint: str = None,  # 从检查点恢复训练的路径

    # ========== 联邦学习核心参数 ==========
    # 这些参数控制联邦学习的行为，是本系统的核心创新
    client_num: int = 3,     # 客户端数量，模拟现实中的多个参与方
    patience: int = 5,       # 早停耐心值，连续多少轮无改善就停止训练
    round: int = 1,          # 每轮联邦学习中每个客户端的本地训练epoch数
    alpha: float = 0.7,      # 聚合权重参数，控制相似度聚合的强度
    beta: int = 1,           # 权重调节参数，影响动态权重计算
    k: int = 20,             # 客户端-服务器分割参数，前k层分配给客户端
):
    """
    联邦学习推荐系统训练主函数

    【核心功能】
    实现基于LLaMA的联邦学习推荐系统训练，包括：
    1. 数据预处理和客户端分配
    2. LoRA参数高效微调
    3. 多客户端本地训练
    4. 基于相似度的模型聚合
    5. 早停机制和性能评估

    【联邦学习流程】
    1. 将用户数据按相似度分配到不同客户端
    2. 每个客户端使用本地数据训练LoRA权重
    3. 计算客户端模型间的相似度矩阵
    4. 基于相似度进行智能模型聚合
    5. 重复上述过程直到收敛或达到最大轮数

    【创新点】
    - 基于用户嵌入的智能数据分割
    - 相似度驱动的模型聚合策略
    - LoRA技术降低计算成本
    - 客户端-服务器混合架构
    """
    # ==================== 第一步：打印训练配置信息 ====================
    # 在训练开始前显示所有超参数，便于调试和记录实验设置
    print("🚀 开始FELLRec联邦学习推荐系统训练")
    print("=" * 60)
    print("📋 训练配置参数:")
    print(
        f"🤖 基础模型: {base_model}\n"
        f"📂 训练数据: {train_data_path}\n"
        f"📂 验证数据: {val_data_path}\n"
        f"📊 样本数量: {sample if sample > 0 else '全部'}\n"
        f"🎲 随机种子: {seed}\n"
        f"💾 输出目录: {output_dir}\n"
        f"📦 批次大小: {batch_size}\n"
        f"🔢 微批次大小: {micro_batch_size}\n"
        f"🔄 训练轮数: {num_epochs}\n"
        f"📈 学习率: {learning_rate}\n"
        f"📏 序列长度: {cutoff_len}\n"
        f"🎯 LoRA秩: {lora_r}\n"
        f"⚖️ LoRA alpha: {lora_alpha}\n"
        f"💧 LoRA dropout: {lora_dropout}\n"
        f"🎪 LoRA目标模块: {lora_target_modules}\n"
        f"📝 输入训练: {train_on_inputs}\n"
        f"📊 按长度分组: {group_by_length}\n"
        f"📈 WandB项目: {wandb_project}\n"
        f"🏃 WandB运行: {wandb_run_name}\n"
        f"👀 WandB监控: {wandb_watch}\n"
        f"💾 WandB日志: {wandb_log_model}\n"
        f"🔄 恢复检查点: {resume_from_checkpoint}\n"
        f'👥 客户端数量: {client_num}\n'
    )
    print("=" * 60)

    # ==================== 第二步：参数验证和基础配置 ====================
    # 验证必需参数
    assert (
        base_model
    ), "❌ 错误：请指定基础模型路径，例如：--base_model='meta-llama/Llama-2-7b-hf'"

    # 配置日志系统，记录训练过程中的重要信息
    logging.basicConfig(
        filename='training.log',  # 日志文件名
        level=logging.INFO,       # 日志级别
        format='%(asctime)s:%(levelname)s:%(message)s'  # 日志格式
    )
    print("📝 日志系统已配置，详细信息将记录到 training.log")

    # 计算梯度累积步数
    # 梯度累积允许我们模拟更大的批次大小而不增加显存使用
    gradient_accumulation_steps = batch_size // micro_batch_size
    print(f"🔢 梯度累积步数: {gradient_accumulation_steps} (批次大小 {batch_size} / 微批次大小 {micro_batch_size})")

    # ==================== 第三步：分布式训练配置 ====================
    # 配置设备映射和分布式训练参数
    device_map = "auto"  # 自动设备映射，让系统自动分配GPU
    world_size = int(os.environ.get("WORLD_SIZE", 1))  # 获取分布式训练的总进程数
    ddp = world_size != 1  # 判断是否使用分布式数据并行(DDP)

    if ddp:
        # 分布式训练模式：每个进程使用指定的GPU
        device_map = {"": int(os.environ.get("LOCAL_RANK") or 0)}
        # 在分布式训练中，每个进程的梯度累积步数需要相应调整
        gradient_accumulation_steps = gradient_accumulation_steps // world_size
        print(f"🌐 启用分布式训练，世界大小: {world_size}")
    else:
        print("💻 使用单机训练模式")

    # ==================== 第四步：WandB监控配置 ====================
    # 配置Weights & Biases实验跟踪（当前被禁用）
    use_wandb = len(wandb_project) > 0 or (
        "WANDB_PROJECT" in os.environ and len(os.environ["WANDB_PROJECT"]) > 0
    )

    # 设置WandB环境变量
    if len(wandb_project) > 0:
        os.environ["WANDB_PROJECT"] = wandb_project
    if len(wandb_watch) > 0:
        os.environ["WANDB_WATCH"] = wandb_watch
    if len(wandb_log_model) > 0:
        os.environ["WANDB_LOG_MODEL"] = wandb_log_model

    # 当前禁用WandB，避免不必要的网络连接
    os.environ["WANDB_DISABLED"] = "true"
    print("📊 WandB监控已禁用（可在需要时启用）")
    # ==================== 第五步：初始化客户端模型 ====================
    # 创建客户端模型字典，用于存储多个客户端的模型实例
    print("🤖 开始初始化客户端模型...")
    client = {}  # 字典结构：{客户端ID: 模型实例}

    # 【关键步骤】加载第一个客户端的基础LLaMA模型
    print("📥 正在加载LLaMA基础模型...")
    client[0] = LlamaForCausalLM.from_pretrained(
        base_model,                    # 基础模型路径
        load_in_8bit=True,            # 使用8位量化，大幅节省显存（约50%）
        torch_dtype=torch.float16,    # 使用半精度浮点数，进一步节省显存
        device_map=device_map,        # 设备映射配置
    )
    print("✅ LLaMA基础模型加载完成")

    # ==================== 联邦学习核心：客户端-服务器模型分割 ====================
    # 这是联邦学习的创新点：将模型分为客户端部分和服务器部分
    print(f"🔄 正在执行客户端-服务器模型分割（前{k}层给客户端）...")
    model_server, model_client = split_client_server(client[0], k)
    # split_client_server函数的作用：
    # - 前k层 + 最后一层（输出层） → 客户端（保护隐私）
    # - 中间层 → 服务器（减少客户端计算负担）

    # 重新合并模型以确保完整性
    print("🔗 正在重新合并模型...")
    merge_model = merge_models(model_client, model_server)
    del model_client, model_server  # 释放临时模型内存

    # 保存合并后的权重并重新加载到客户端模型
    weights = merge_model.state_dict()  # 获取模型权重
    del merge_model  # 释放合并模型内存
    client[0].load_state_dict(weights)  # 将权重加载到客户端模型
    del weights  # 释放权重内存
    print("✅ 客户端模型初始化完成")

    # 分布式训练同步点：确保所有进程都完成了模型初始化
    dist.barrier()

    # 注释掉的代码块：另一种模型初始化方式（已废弃）
    # del base_client
    # dist.barrier()
    # # load dict for base_client
    # client[0].load_state_dict(weights)
    # del model_client, model_server
    # torch.cuda.empty_cache()
    # dist.barrier()

    # ==================== 第六步：初始化分词器 ====================
    # 分词器负责将文本转换为模型可理解的数字序列
    print("🔤 正在初始化LLaMA分词器...")
    tokenizer = LlamaTokenizer.from_pretrained(base_model)

    # 配置特殊token
    tokenizer.pad_token_id = 0  # 设置填充token为0（unk token）
    # 重要：填充token必须与结束token不同，避免模型混淆

    tokenizer.padding_side = "left"  # 设置左侧填充，支持批量推理
    # 左侧填充确保生成的文本从右侧开始，符合语言模型的生成习惯
    print("✅ 分词器配置完成")

    # ==================== 文本分词函数 ====================
    def tokenize(prompt, add_eos_token=True):
        """
        文本分词函数 - 推荐系统数据预处理的核心函数

        【功能说明】
        将推荐任务的文本提示转换为模型可处理的token序列。
        这是将推荐问题转换为语言生成问题的关键步骤。

        【处理流程】
        1. 将文本转换为token ID序列
        2. 添加注意力掩码（告诉模型哪些位置是真实内容）
        3. 可选添加结束token
        4. 创建标签序列（用于计算损失）

        参数:
            prompt (str): 输入的推荐提示文本
            add_eos_token (bool): 是否添加结束token

        返回:
            dict: 包含input_ids, attention_mask, labels的字典
        """
        # 使用分词器处理文本
        result = tokenizer(
            prompt,
            truncation=True,        # 截断过长的序列
            max_length=cutoff_len,  # 最大长度限制
            padding=False,          # 暂不填充（批处理时再填充）
            return_tensors=None,    # 返回Python列表而非张量
        )

        # 【重要】检查并添加结束token
        # 结束token告诉模型这里是序列的结束，对生成质量很重要
        if (
            result["input_ids"][-1] != tokenizer.eos_token_id  # 最后一个token不是结束token
            and len(result["input_ids"]) < cutoff_len          # 序列长度未达到上限
            and add_eos_token                                  # 需要添加结束token
        ):
            result["input_ids"].append(tokenizer.eos_token_id)  # 添加结束token ID
            result["attention_mask"].append(1)                  # 对应的注意力掩码为1

        # 创建标签序列：在语言模型训练中，标签就是输入序列本身
        # 模型学习预测下一个token，所以标签是输入序列
        result["labels"] = result["input_ids"].copy()

        return result

    # ==================== 推荐数据预处理函数 ====================
    def generate_and_tokenize_prompt(data_point):
        """
        生成并分词化推荐提示 - 推荐系统的核心数据处理函数

        【功能说明】
        这个函数是推荐系统的关键组件，它将结构化的推荐数据
        转换为大语言模型可以理解和学习的格式。

        【处理流程】
        1. 将推荐数据转换为自然语言提示
        2. 对提示进行分词处理
        3. 根据训练策略调整标签（决定模型学习什么）

        【训练策略】
        - train_on_inputs=True: 整个序列都参与损失计算
        - train_on_inputs=False: 只在输出部分计算损失

        参数:
            data_point (dict): 推荐数据点，包含instruction, input, output

        返回:
            dict: 分词后的数据，包含input_ids, attention_mask, labels
        """
        # 生成完整的推荐提示（包含指令、输入、输出）
        full_prompt = generate_prompt(data_point)

        # 对完整提示进行分词
        tokenized_full_prompt = tokenize(full_prompt)

        # 【重要】训练策略选择：是否在输入部分也计算损失
        if not train_on_inputs:
            # 如果不在输入上训练，只在输出部分计算损失
            # 这意味着模型只学习生成推荐结果，不学习重复输入

            # 生成只包含指令和输入的提示（没有输出）
            user_prompt = generate_prompt({**data_point, "output": ""})
            tokenized_user_prompt = tokenize(user_prompt, add_eos_token=False)
            user_prompt_len = len(tokenized_user_prompt["input_ids"])

            # 将输入部分的标签设为-100（PyTorch会忽略这些位置的损失）
            # 只在输出部分计算损失，让模型专注学习生成推荐
            tokenized_full_prompt["labels"] = [
                -100  # 忽略损失的特殊值
            ] * user_prompt_len + tokenized_full_prompt["labels"][
                user_prompt_len:  # 保留输出部分的标签
            ]
        return tokenized_full_prompt

    # ==================== 第七步：准备模型进行LoRA训练 ====================
    print("🔧 正在准备模型进行8位训练...")
    # 为8位量化训练准备模型（节省显存的重要步骤）
    client[0] = prepare_model_for_int8_training(client[0])

    # ==================== 第八步：配置LoRA参数 ====================
    print("⚙️ 正在配置LoRA参数...")
    # LoRA (Low-Rank Adaptation) 配置
    # 这是参数高效微调的关键技术，只训练少量参数就能达到全参数微调的效果
    config = LoraConfig(
        r=lora_r,                           # LoRA的秩（rank），控制新增参数量
        lora_alpha=lora_alpha,              # LoRA的缩放参数，影响LoRA权重的重要性
        target_modules=lora_target_modules, # 要应用LoRA的模块（通常是注意力层）
        lora_dropout=lora_dropout,          # LoRA层的dropout率，防止过拟合
        bias="none",                        # 不训练偏置参数
        task_type="CAUSAL_LM",             # 任务类型：因果语言模型（生成式任务）
    )

    # 将LoRA配置应用到模型上
    print("🎯 正在应用LoRA配置到模型...")
    client[0] = get_peft_model(client[0], config)
    print("✅ LoRA配置完成")

    # ==================== 第九步：加载和预处理推荐数据集 ====================
    print("📂 正在加载推荐数据集...")

    # 初始化数据列表，用于存储多个数据文件
    train_data_list = []  # 训练数据列表
    val_data_list = []    # 验证数据列表
    test_data_list = []   # 测试数据列表

    # 【数据加载】加载训练数据
    print("📥 加载训练数据...")
    for path in train_data_path:
        if path.endswith(".json"):
            # 如果是JSON文件，直接加载
            train_data_list.append(load_dataset("json", data_files=path))
        else:
            # 如果是HuggingFace数据集名称，从Hub加载
            train_data_list.append(load_dataset(path))

    # 加载验证数据
    print("📥 加载验证数据...")
    for path in val_data_path:
        if path.endswith(".json"):
            val_data_list.append(load_dataset("json", data_files=path))
        else:
            val_data_list.append(load_dataset(path))

    # 加载测试数据
    print("📥 加载测试数据...")
    for path in test_data_path:
        if path.endswith(".json"):
            test_data_list.append(load_dataset("json", data_files=path))
        else:
            test_data_list.append(load_dataset(path))

    # ==================== 第十步：数据预处理和分词 ====================
    print("🔄 正在预处理训练数据...")
    for i in range(len(train_data_list)):
        # 【数据采样和打乱】
        if sample > -1:
            # 如果指定了采样数量，先打乱再选择指定数量的样本
            train_data_list[i]["train"] = train_data_list[i]["train"].shuffle(seed=seed).select(range(sample))
        else:
            # 否则使用全部数据，但仍然打乱
            train_data_list[i]["train"] = train_data_list[i]["train"].shuffle(seed=seed)

        # 再次打乱确保随机性
        train_data_list[i]["train"] = train_data_list[i]["train"].shuffle(seed=seed)

        # 【关键步骤】对每个数据点应用推荐数据预处理
        # 这一步将原始推荐数据转换为模型可以理解的token序列
        train_data_list[i] = train_data_list[i].map(lambda x: generate_and_tokenize_prompt(x))

    print("🔄 正在预处理验证数据...")
    for i in range(len(val_data_list)):
        # 验证数据也需要相同的预处理
        val_data_list[i] = val_data_list[i].map(lambda x: generate_and_tokenize_prompt(x))

    print("🔄 正在预处理测试数据...")
    for i in range(len(test_data_list)):
        # 测试数据同样需要预处理
        test_data_list[i] = test_data_list[i].map(lambda x: generate_and_tokenize_prompt(x))

    # 【数据合并】将多个数据文件合并为单一数据集
    print("🔗 正在合并数据集...")
    train_data = concatenate_datasets([_["train"] for _ in train_data_list])
    val_data = concatenate_datasets([_["train"] for _ in val_data_list])
    test_data = concatenate_datasets([_["train"] for _ in test_data_list])
    print(f"✅ 数据加载完成 - 训练:{len(train_data)}, 验证:{len(val_data)}, 测试:{len(test_data)}")

    # ==================== 第十一步：联邦学习数据分割 ====================
    # 这是联邦学习的核心步骤：将数据分配给不同的客户端
    print("🔄 正在进行联邦学习数据分割...")

    # 检查是否已经存在分割好的客户端数据文件
    # 如果存在就直接加载，避免重复计算（节省时间）
    pkl_files_exist = (
        os.path.exists('./data/train_client_data.pkl') and
        os.path.exists('./data/valid_client_data.pkl') and
        os.path.exists('./data/test_client_data.pkl')
    )

    if not pkl_files_exist:
        print("🆕 首次运行，正在根据用户嵌入进行智能数据分割...")
        # 【核心算法】使用预训练的用户嵌入进行智能分割
        # split_dataset函数的工作原理：
        # 1. 加载预训练的用户嵌入向量
        # 2. 使用K-means聚类将相似用户分组
        # 3. 将每组用户的数据分配到对应的客户端
        # 这确保相似的用户被分配到同一个客户端，提高联邦学习效果
        client_data, val_data, test_data = split_dataset(
            train_data, client_num, val_data, test_data, pretrain_emb_path
        )

        # 保存分割结果，下次运行时可以直接加载
        print("💾 保存客户端数据分割结果...")
        with open('./data/train_client_data.pkl', 'wb') as file:
            pickle.dump(client_data, file)
        with open('./data/valid_client_data.pkl', 'wb') as file:
            pickle.dump(val_data, file)
        with open('./data/test_client_data.pkl', 'wb') as file:
            pickle.dump(test_data, file)
    else:
        print("📁 发现已存在的数据分割文件，正在加载...")
        # 直接加载已经分割好的数据
        with open('./data/train_client_data.pkl', 'rb') as file:
            client_data = pickle.load(file)
        with open('./data/valid_client_data.pkl', 'rb') as file:
            val_data = pickle.load(file)
        with open('./data/test_client_data.pkl', 'rb') as file:
            test_data = pickle.load(file)

    # ==================== 第十二步：打印数据分布信息 ====================
    # 只在主进程（rank 0）中打印信息，避免重复输出
    if dist.get_rank() == 0:
        print(f"\n📊 === 联邦学习数据分布信息 ===")
        logging.info(f'客户端数量: {client_num}')

        # 打印每个客户端的训练样本数量
        print("📈 训练数据分布:")
        for cnt, client_ in enumerate(client_data):
            sample_count = len(client_)
            print(f"  客户端 {cnt}: {sample_count} 个训练样本")
            logging.info(f'客户端 {cnt} 有 {sample_count} 个训练样本')

        # 打印每个客户端的验证样本数量
        print("📊 验证数据分布:")
        for cnt, client_ in enumerate(val_data):
            sample_count = len(client_)
            print(f"  客户端 {cnt}: {sample_count} 个验证样本")
            logging.info(f'客户端 {cnt} 有 {sample_count} 个验证样本')

        # 打印每个客户端的测试样本数量
        print("🎯 测试数据分布:")
        for cnt, client_ in enumerate(test_data):
            sample_count = len(client_)
            print(f"  客户端 {cnt}: {sample_count} 个测试样本")
            logging.info(f'客户端 {cnt} 有 {sample_count} 个测试样本')
        print("=" * 50)

    # 打印模型的可训练参数信息
    # 这让我们了解LoRA实际训练了多少参数（通常只有原模型的1-2%）
    print("\n🎯 === 模型参数信息 ===")
    client[0].print_trainable_parameters()  # 显示可训练参数的百分比

    # ==================== 第十三步：初始化联邦学习训练参数 ====================
    print("\n🚀 === 初始化联邦学习训练参数 ===")

    # 【早停机制】为每个客户端记录最佳验证损失
    best_eval_loss = [1e5 for _ in range(client_num)]  # 初始化为很大的值
    best_eval_loss_all = 1e5  # 全局最佳验证损失

    # 【学习率调度】预热步数设置
    warmup_step = 20  # 开始时使用较小的学习率，逐渐增加

    # 计算每个epoch的更新步数（用于学习率调度）
    num_update_steps_per_epoch = len(client_data[0]) // gradient_accumulation_steps
    num_update_steps_per_epoch = max(num_update_steps_per_epoch, 1)  # 至少1步

    # 【联邦聚合】初始化客户端权重参数
    warm_weight = [0 for _ in range(len(client_data))]  # 每个客户端的聚合权重

    print(f"📊 每个epoch预计更新步数: {num_update_steps_per_epoch}")
    print(f"🔥 学习率预热步数: {warmup_step}")
    print(f"⚖️ 客户端聚合权重已初始化")

    # ==================== 第十四步：开始联邦学习训练循环 ====================
    print(f"\n🎯 === 开始联邦学习训练，共 {num_epochs} 个epoch ===")
    print("=" * 70)

    for epoch in range(num_epochs):
        print(f"\n📅 第 {epoch + 1}/{num_epochs} 轮联邦学习开始")

        # 初始化本轮训练的变量
        train_loss = []        # 记录每个客户端的训练损失
        client_trainer = {}    # 存储每个客户端的训练器
        eval_trainer = {}      # 存储每个客户端的评估器
        eval_results = []      # 记录每个客户端的评估结果

        # 【文件管理策略】交替保存策略，避免文件冲突
        # 奇偶epoch使用不同的文件名，确保训练稳定性
        if epoch % 2 == 0:
            save_name = f'ori'      # 当前epoch保存的文件名后缀
            update_name = f'update' # 下一步更新时使用的文件名后缀
        else:
            save_name = f'update'
            update_name = f'ori'

        print(f"📁 本轮使用保存策略: {save_name} -> {update_name}")
        # ==================== 客户端本地训练阶段 ====================
        for i in range(client_num):
            print(f"\n🔧 正在准备客户端 {i + 1}/{client_num} 的模型...")

            # 【第一个epoch】初始化其他客户端模型（客户端0已在前面初始化）
            if epoch == 0 and i != 0:
                print(f"  🆕 初始化客户端 {i} 的基础模型...")
                # 为每个客户端创建独立的LLaMA模型实例
                client[i] = LlamaForCausalLM.from_pretrained(
                        base_model,                    # 基础模型路径
                        load_in_8bit=True,            # 8位量化节省显存
                        torch_dtype=torch.float16,    # 半精度浮点数
                        device_map=device_map,        # 设备映射
                    )

                # 【联邦学习核心】执行客户端-服务器分割
                print(f"  🔄 执行客户端 {i} 的模型分割...")
                model_server, model_client = split_client_server(client[i], k)

                # 重新合并模型确保完整性
                merge_model = merge_models(model_client, model_server)
                del model_client, model_server  # 释放临时模型内存

                # 加载合并后的权重
                weights = merge_model.state_dict()
                del merge_model  # 释放合并模型内存
                client[i].load_state_dict(weights)
                del weights  # 释放权重内存

                # 【LoRA配置】为客户端模型应用LoRA
                print(f"  ⚙️ 为客户端 {i} 配置LoRA...")
                client[i] = prepare_model_for_int8_training(client[i])
                client[i] = get_peft_model(client[i], config)

            # 【后续epoch】从聚合结果加载模型
            if epoch != 0:
                warmup_step = 0  # 第一轮后不再需要预热
                print(f"  🔄 从聚合结果重新加载客户端 {i} 的模型...")

                # 重新加载基础模型
                client[i] = LlamaForCausalLM.from_pretrained(
                    base_model,
                    load_in_8bit=True,
                    torch_dtype=torch.float16,
                    device_map=device_map,
                )

                # 准备模型并应用LoRA配置
                client[i] = prepare_model_for_int8_training(client[i])
                client[i] = get_peft_model(client[i], config)

                # 【关键步骤】加载上一轮聚合后的LoRA权重
                print(f"  📥 加载客户端 {i} 的聚合权重...")
                state_dict = torch.load(f'{output_dir}/client{i}_{save_name}/adapter_model.bin')
                client[i] = set_peft_model_state_dict(client[i], state_dict)

            # 【多GPU配置】配置模型并行（如果可用）
            if not ddp and torch.cuda.device_count() > 1:
                print(f"  🚀 启用客户端 {i} 的模型并行...")
                client[i].is_parallelizable = True  # 允许模型并行
                client[i].model_parallel = True     # 启用模型并行

            # 只在主进程中打印训练信息
            if dist.get_rank() == 0:
                print(f"🏃‍♂️ 开始训练客户端 {i}，第 {epoch + 1} 轮")
                logging.info(f'训练客户端 {i}，第 {epoch} 轮')

            # ==================== 配置客户端训练器 ====================
            print(f"  ⚙️ 配置客户端 {i} 的训练器...")
            client_trainer[i] = Trainer(
                model=client[i],                    # 当前客户端的模型
                train_dataset=client_data[i],       # 当前客户端的训练数据
                callbacks=[LoggingCallback],        # 自定义日志回调
                args=transformers.TrainingArguments(
                    # 【批次配置】
                    per_device_train_batch_size=micro_batch_size,      # 每个设备的批次大小
                    per_device_eval_batch_size=micro_batch_size,       # 评估时的批次大小
                    gradient_accumulation_steps=gradient_accumulation_steps, # 梯度累积步数

                    # 【学习率配置】
                    warmup_steps=warmup_step,           # 预热步数
                    learning_rate=learning_rate,        # 学习率

                    # 【训练配置】
                    num_train_epochs=round,             # 本地训练轮数（联邦学习中每个客户端的训练轮数）

                    # 【性能优化】
                    fp16=True,                          # 使用半精度训练，节省显存
                    optim="adamw_torch",                # 使用AdamW优化器

                    # 【日志配置】
                    logging_strategy="steps",           # 按步数记录日志
                    logging_steps=4,                    # 每4步记录一次

                    # 【保存配置】
                    save_strategy="steps",              # 按步数保存
                    save_total_limit=1,                 # 最多保存1个检查点
                    output_dir=output_dir,              # 输出目录

                    # 【分布式训练配置】
                    ddp_find_unused_parameters=False if ddp else None,

                    # 【数据处理配置】
                    group_by_length=group_by_length,    # 是否按长度分组

                    # 【监控配置】
                    report_to=None,                     # 不上报到外部监控平台
                ),
                # 【数据整理器】处理批次数据的填充和格式化
                data_collator=transformers.DataCollatorForSeq2Seq(
                    tokenizer,                          # 分词器
                    pad_to_multiple_of=8,              # 填充到8的倍数（优化GPU计算）
                    return_tensors="pt",               # 返回PyTorch张量
                    padding=True                       # 启用填充
                ))

            # 【性能优化】禁用缓存以节省内存
            client[i].config.use_cache = False

            # ==================== 执行客户端本地训练 ====================
            print(f"  🚀 客户端 {i} 开始本地训练...")
            client_trainer[i].train()  # 执行训练

            # 【模型保存】保存训练后的LoRA权重
            print(f"  💾 保存客户端 {i} 的训练结果...")
            client[i].save_pretrained(f'{output_dir}/client{i}_{save_name}')

            # 【损失记录】记录训练损失（用于后续的聚合权重计算）
            final_loss = client_trainer[i].state.log_history[-1]['train_loss']
            train_loss.append(final_loss)
            print(f"  ✅ 客户端 {i} 训练完成，最终损失: {final_loss:.4f}")

            # 【内存管理】释放GPU内存，为下一个客户端做准备
            print(f"  🧹 释放客户端 {i} 的GPU内存...")
            del client[i], client_trainer[i]
            torch.cuda.empty_cache()  # 清空CUDA缓存

        # ==================== 联邦模型聚合阶段 ====================
        print(f"\n🔄 开始第 {epoch + 1} 轮的联邦模型聚合...")

        # 【核心算法1】计算客户端之间的相似度矩阵和聚合参数
        print("  📊 计算客户端相似度矩阵...")
        sim_matrix, accumulated_params = aggregate(output_dir, device_map, client_num, save_name, base_model)
        # aggregate函数的作用：
        # 1. 加载所有客户端的LoRA权重
        # 2. 计算客户端模型之间的余弦相似度
        # 3. 准备聚合所需的参数

        # 【核心算法2】将训练损失转换为权重分布
        print("  ⚖️ 计算客户端聚合权重...")
        train_loss = softmax_with_temperature(train_loss)
        # 损失越小的客户端，在聚合时权重越大
        print(f"  客户端权重分布: {[f'{w:.3f}' for w in train_loss]}")

        # ==================== 更新每个客户端模型 ====================
        print("  🔄 根据聚合结果更新各客户端模型...")
        for i in range(client_num):
            print(f"\n    🔧 更新客户端 {i} 的模型...")

            # 【步骤1】重新加载基础模型
            client[i] = LlamaForCausalLM.from_pretrained(
                base_model,
                load_in_8bit=True,
                torch_dtype=torch.float16,
                device_map=device_map,
            )

            # 【步骤2】加载该客户端训练后的LoRA权重
            print(f"      📥 加载客户端 {i} 的LoRA权重...")
            client[i] = PeftModel.from_pretrained(
                        client[i],
                        f'{output_dir}/client{i}_{save_name}',  # 客户端训练后的权重路径
                        torch_dtype=torch.float16,
                        device_map=device_map,
                    )

            # 【步骤3】计算该客户端的动态聚合权重
            # 这个公式结合了训练损失和epoch进度，动态调整聚合强度
            warm_weight[i] = math.tanh(alpha/(train_loss[i]**(epoch+1/beta)))
            print(f"      ⚖️ 客户端 {i} 的动态权重: {warm_weight[i]:.4f}")

            # 【步骤4】获取聚合后的LoRA权重
            # 这里融合了所有客户端的知识，根据相似度进行加权平均
            print(f"      🧮 计算客户端 {i} 的聚合权重...")
            lora_weight = get_aggregate_lora_weight(i, sim_matrix, accumulated_params, warm_weight[i], beta)
            # get_aggregate_lora_weight函数的作用：
            # 1. 根据相似度矩阵确定相似客户端
            # 2. 按权重聚合相似客户端的LoRA参数
            # 3. 返回聚合后的模型权重

            # 【步骤5】将聚合后的权重加载到模型中
            client[i].load_state_dict(lora_weight, strict=False)

            # 【步骤6】保存更新后的模型
            print(f"      💾 保存客户端 {i} 的更新模型...")
            client[i].save_pretrained(f'{output_dir}/client{i}_{update_name}')

            # ==================== 客户端模型性能评估 ====================
            print(f"      📊 评估客户端 {i} 的聚合后模型性能...")

            # 【步骤1】将模型设置为评估模式
            client[i].eval()  # 关闭dropout等训练特有的层

            # 【步骤2】配置评估训练器
            eval_trainer[i] = Trainer(
                                model=client[i],                    # 聚合后的客户端模型
                                eval_dataset=val_data[i],           # 该客户端的验证数据
                                args=transformers.TrainingArguments(
                                    # 批次设置（评估时通常可以用更大的批次）
                                    per_device_train_batch_size=micro_batch_size,
                                    per_device_eval_batch_size=micro_batch_size,
                                    gradient_accumulation_steps=gradient_accumulation_steps,

                                    # 学习率设置（评估时不需要，但必须提供）
                                    warmup_steps=warmup_step,
                                    learning_rate=learning_rate,
                                    num_train_epochs=1,              # 评估只需要1个epoch

                                    # 精度设置
                                    fp16=True,                       # 使用半精度加速评估

                                    # 日志设置
                                    logging_steps=8,                 # 评估时的日志频率
                                    optim="adamw_torch",             # 优化器（评估时不使用）

                                    # 评估策略
                                    evaluation_strategy="steps",     # 按步数评估
                                    # eval_steps=1,                  # 评估步数（已注释）

                                    # 保存设置
                                    save_strategy="steps",
                                    # save_steps=1,                  # 保存步数（已注释）
                                    output_dir=output_dir,
                                    save_total_limit=1,
                                    # load_best_model_at_end=True,   # 是否加载最佳模型（已注释）

                                    # 分布式设置
                                    ddp_find_unused_parameters=False if ddp else None,
                                    group_by_length=group_by_length,

                                    # 禁用外部监控
                                    report_to=None,
                                ),
                                # 数据整理器
                                data_collator=transformers.DataCollatorForSeq2Seq(
                                                tokenizer,
                                                pad_to_multiple_of=8,
                                                return_tensors="pt",
                                                padding=True))

            # 【步骤3】执行评估
            print(f"      🔍 执行评估...")
            eval_info = eval_trainer[i].evaluate()
            current_eval_loss = eval_info["eval_loss"]
            eval_results.append(current_eval_loss)

            # 【步骤4】记录评估结果
            if dist.get_rank() == 0:
                print(f"      ✅ 客户端 {i} 评估损失: {current_eval_loss:.4f}")
                logging.info(f'第 {epoch} 轮: 客户端 {i} 评估结果: {current_eval_loss}')

            # ==================== 最佳模型保存机制 ====================
            # 【早停机制】检查是否是该客户端的最佳模型
            if current_eval_loss < best_eval_loss[i]:
                print(f"      🏆 客户端 {i} 达到新的最佳性能！")
                best_eval_loss[i] = current_eval_loss

                # 【重要技巧】保存最佳模型时只保存LoRA参数
                # 重写state_dict方法以只保存LoRA参数，节省存储空间
                old_state_dict = client[i].state_dict
                client[i].state_dict = (
                    lambda self, *_, **__: get_peft_model_state_dict(
                        self, old_state_dict()
                    )
                ).__get__(client[i], type(client[i]))

                # 【性能优化】如果支持，使用torch.compile优化模型
                if torch.__version__ >= "2" and sys.platform != "win32":
                    print(f"      ⚡ 编译客户端 {i} 的模型以提升性能...")
                    client[i] = torch.compile(client[i])

                # 保存最佳模型
                print(f"      💾 保存客户端 {i} 的最佳模型...")
                client[i].save_pretrained(f'{output_dir}/best_client{i}_model')
            # 【内存清理】清理评估相关的内存
            print(f"      🧹 清理客户端 {i} 的评估内存...")
            del eval_trainer[i], client[i]
            torch.cuda.empty_cache()

        # ==================== 计算全局评估结果 ====================
        print(f"\n📈 计算第 {epoch + 1} 轮的全局评估结果...")

        # 【加权平均】计算全局评估损失
        # 权重是每个客户端的验证样本数量，确保样本多的客户端有更大影响
        sum_ = 0
        all_eval_num = 0
        for cnt, client_eval in enumerate(eval_results):
            client_weight = len(val_data[cnt])  # 该客户端的样本数量作为权重
            sum_ += client_eval * client_weight  # 加权求和
            all_eval_num += client_weight        # 总权重

        # 计算全局加权平均评估损失
        global_eval_loss = sum_ / all_eval_num

        # 记录全局评估结果
        if dist.get_rank() == 0:
            print(f"🌍 第 {epoch + 1} 轮全局评估损失: {global_eval_loss:.4f}")
            logging.info(f'第 {epoch} 轮: 全局评估结果: {global_eval_loss}')

        # ==================== 早停机制 ====================
        # 早停是防止过拟合的重要技术：如果模型性能不再提升，提前停止训练
        print("🛑 检查早停条件...")
        if global_eval_loss < best_eval_loss_all:
            # 如果当前性能是历史最佳
            print(f"🎉 达到新的全局最佳性能: {global_eval_loss:.4f}")
            best_eval_loss_all = global_eval_loss
            early_stop = 0  # 重置早停计数器
        else:
            # 如果性能没有提升
            early_stop += 1
            print(f"⚠️ 性能未提升，早停计数: {early_stop}/{patience}")

        # 检查是否满足早停条件
        if early_stop >= patience and epoch > 10:
            print(f"🛑 触发早停机制！连续 {patience} 轮无改善，训练结束。")
            print(f"📊 最佳全局评估损失: {best_eval_loss_all:.4f}")
            break

        # 清理GPU缓存，为下一轮训练做准备
        torch.cuda.empty_cache()

        print(f"✅ 第 {epoch + 1} 轮联邦学习完成")
        print("=" * 70)

    # ==================== 训练完成总结 ====================
    print(f"\n🎊 联邦学习训练完成！")
    print(f"📊 最终全局最佳评估损失: {best_eval_loss_all:.4f}")
    print(f"🏆 各客户端最佳性能:")
    for i, best_loss in enumerate(best_eval_loss):
        print(f"   客户端 {i}: {best_loss:.4f}")
    print("🎯 最佳模型已保存到各客户端的 best_client{i}_model 目录")
        

# ==================== 推荐提示生成函数 ====================
def generate_prompt(data_point):
    """
    生成推荐任务的训练提示 - 推荐系统数据格式化的核心函数

    【功能说明】
    这个函数是推荐系统的关键组件，它将结构化的推荐数据
    转换为大语言模型能理解的自然语言提示格式。

    【提示格式】
    使用Alpaca指令格式，包含三个部分：
    1. Instruction: 推荐任务的描述
    2. Input: 用户的历史行为数据（可选）
    3. Response: 期望的推荐结果

    【示例】
    输入数据：
    {
        "instruction": "根据用户的游戏历史，推荐下一个可能喜欢的游戏",
        "input": "用户玩过：《塞尔达传说》、《超级马里奥》",
        "output": "推荐：《马里奥卡丁车》"
    }

    输出提示：
    Below is an instruction that describes a task, paired with an input...
    ### Instruction:
    根据用户的游戏历史，推荐下一个可能喜欢的游戏
    ### Input:
    用户玩过：《塞尔达传说》、《超级马里奥》
    ### Response:
    推荐：《马里奥卡丁车》

    参数:
        data_point (dict): 推荐数据点，包含instruction, input, output字段

    返回:
        str: 格式化的训练提示文本
    """

    if data_point["input"]:
        # 有输入信息的情况：包含指令、输入和响应三部分
        return f"""Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.

                ### Instruction:
                {data_point["instruction"]}

                ### Input:
                {data_point["input"]}

                ### Response:
                {data_point["output"]}"""
    else:
        # 无输入信息的情况：只包含指令和响应两部分
        return f"""Below is an instruction that describes a task. Write a response that appropriately completes the request.

                ### Instruction:
                {data_point["instruction"]}

                ### Response:
                {data_point["output"]}"""


# ==================== 程序入口点 ====================
if __name__ == "__main__":
    """
    FELLRec联邦学习推荐系统训练脚本入口

    使用Fire库将train函数转换为命令行接口，支持以下调用方式：

    【基本用法】
    python finetune.py --base_model "path/to/llama-7b"

    【完整参数示例】
    python finetune.py \
        --base_model "meta-llama/Llama-2-7b-hf" \
        --train_data_path '["./data/games/train_1024_user.json"]' \
        --val_data_path '["./data/games/valid_5000_user.json"]' \
        --client_num 5 \
        --num_epochs 10 \
        --learning_rate 1e-4 \
        --lora_r 8 \
        --alpha 0.7

    【参数说明】
    - base_model: LLaMA基础模型路径（必需）
    - client_num: 联邦学习客户端数量
    - num_epochs: 训练轮数
    - learning_rate: 学习率
    - lora_r: LoRA秩
    - alpha: 联邦聚合参数

    更多参数请参考train函数的参数定义。
    """
    print("🚀 启动FELLRec联邦学习推荐系统训练脚本...")
    fire.Fire(train)