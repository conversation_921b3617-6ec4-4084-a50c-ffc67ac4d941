"""
FELLRec联邦学习推荐系统 - 推理脚本

==============================================================================
脚本功能说明：
==============================================================================
这个脚本是FELLRec系统的推理模块，用于加载训练好的联邦学习推荐模型，
对测试数据进行推理，生成个性化推荐结果。

主要功能：
1. 加载每个客户端训练好的最佳模型
2. 对测试数据进行批量推理
3. 生成推荐结果并保存为JSON格式
4. 支持多种硬件设备（GPU/CPU/MPS）

推理流程：
客户端数据加载 → 模型加载 → 批量推理 → 结果保存 → 下一个客户端

注意事项：
- 每个客户端使用独立的模型进行推理
- 推理结果会合并保存到统一的JSON文件中
- 支持批量处理以提高推理效率
==============================================================================
"""

# ==================== 系统和基础库导入 ====================
import sys  # 系统相关功能，用于平台检测

import fire  # 命令行接口库，将函数转换为命令行工具
import gradio as gr  # Web界面库（本脚本中未使用，但保留以备扩展）
import torch  # PyTorch深度学习框架
torch.set_num_threads(1)  # 限制PyTorch使用的CPU线程数，避免资源竞争

import transformers  # HuggingFace Transformers库，用于加载预训练模型
import json  # JSON数据处理库
import os  # 操作系统接口库

# ==================== 性能优化环境变量设置 ====================
# 这些设置是为了避免多线程冲突和提高推理性能
os.environ['OPENBLAS_NUM_THREADS'] = '1'  # 限制OpenBLAS库的线程数
os.environ['OMP_NUM_THREADS'] = '1'       # 限制OpenMP的线程数

# ==================== GPU设备配置 ====================
os.environ['CUDA_VISIBLE_DEVICES'] = '0'  # 指定使用第0号GPU设备

# ==================== 模型相关库导入 ====================
from peft import PeftModel  # PEFT库，用于加载LoRA微调后的模型
from transformers import GenerationConfig, LlamaTokenizer  # 生成配置和分词器
from transformers import LlamaForCausalLM  # LLaMA因果语言模型
import pickle  # Python对象序列化库，用于加载客户端数据

# ==================== 设备自动检测和配置 ====================
# 这部分代码自动检测可用的计算设备，优先使用GPU加速推理
if torch.cuda.is_available():
    device = "cuda"  # 如果有NVIDIA GPU，使用CUDA
    print("🚀 检测到CUDA设备，将使用GPU加速推理")
else:
    device = "cpu"   # 否则使用CPU
    print("💻 未检测到CUDA设备，将使用CPU进行推理")

# 检测Apple Silicon的MPS支持（适用于M1/M2 Mac）
try:
    if torch.backends.mps.is_available():
        device = "mps"  # 使用Apple的Metal Performance Shaders
        print("🍎 检测到MPS设备，将使用Apple Silicon GPU加速")
except:  # noqa: E722
    pass  # 如果MPS不可用，保持之前的设备选择


def main(
    load_8bit: bool = False,
    base_model: str = " ",
    lora_weights: str = "./model/games/1_1024/best_model",
    test_data_path: str = "data/games/test.json",
    result_json_data: str = "games.json",
    batch_size: int=1,
):
    assert (
        base_model
    ), "Please specify a --base_model, e.g. --base_model='decapoda-research/llama-7b-hf'"

    with open('./data/test_client_data.pkl', 'rb') as file:
        test_data_all = pickle.load(file)
    result = []
    for client_idx in range(len(test_data_all)):
        lora_weights = f'./model/games/1_1024/best_client{client_idx}_model'
        tokenizer = LlamaTokenizer.from_pretrained(base_model)
        if device == "cuda":
            model = LlamaForCausalLM.from_pretrained(
                base_model,
                load_in_8bit=load_8bit,
                torch_dtype=torch.float16,
                device_map="auto",
            )
            model = PeftModel.from_pretrained(
                model,
                lora_weights,
                torch_dtype=torch.float16,
                device_map={'': 0}
            )
        elif device == "mps":
            model = LlamaForCausalLM.from_pretrained(
                base_model,
                device_map={"": device},
                torch_dtype=torch.float16,
            )
            model = PeftModel.from_pretrained(
                model,
                lora_weights,
                device_map={"": device},
                torch_dtype=torch.float16,
            )
        else:
            model = LlamaForCausalLM.from_pretrained(
                base_model, device_map={"": device}, low_cpu_mem_usage=True
            )
            model = PeftModel.from_pretrained(
                model,
                lora_weights,
                device_map={"": device},
            )

        tokenizer.padding_side = "left"


        # unwind broken decapoda-research config
        model.config.pad_token_id = tokenizer.pad_token_id = 0  # unk
        model.config.bos_token_id = 1
        model.config.eos_token_id = 2

        if not load_8bit:
            model.half()  # seems to fix bugs for some users.

        model.eval()
        if torch.__version__ >= "2" and sys.platform != "win32":
            model = torch.compile(model)

        def evaluate(
            instructions,
            inputs=None,
            temperature=0,
            top_p=0.9,
            top_k=40,
            num_beams=4,
            max_new_tokens=128,
            **kwargs,
        ):
            prompt = [generate_prompt(instruction, input) for instruction, input in zip(instructions, inputs)]
            # print(prompt)
            inputs = tokenizer(prompt, return_tensors="pt", padding=True, truncation=True, max_length=512).to(device)
            generation_config = GenerationConfig(
                temperature=temperature,
                top_p=top_p,
                top_k=top_k,
                num_beams=num_beams,
                num_return_sequences=1,
                **kwargs,
            )
            with torch.no_grad():
                generation_output = model.generate(
                    **inputs,
                    generation_config=generation_config,
                    return_dict_in_generate=True,
                    output_scores=True,
                    max_new_tokens=max_new_tokens,
                )
            # ipdb.set_trace()
            s = generation_output.sequences
            output = tokenizer.batch_decode(s, skip_special_tokens=True)
            output = [_.split('Response:\n')[-1] for _ in output]
            # real_outputs = [output[i * num_beams: (i + 1) * num_beams] for i in range(len(output) // num_beams)]
            real_outputs = output
            return real_outputs


        outputs = []
        from tqdm import tqdm
        test_data = test_data_all[client_idx]
        # with open(test_data_path, 'r') as f:
        #     test_data = json.load(f)
        print(f"length of test data: {len(test_data)}")
        # test_data=test_data[:10]
        # print(f"length of test data: {len(test_data)}")
        instructions = [_['instruction'] for _ in test_data]
        inputs = [_['input'] for _ in test_data]
        def batch(list, batch_size=batch_size):
            chunk_size = (len(list) - 1) // batch_size + 1
            for i in range(chunk_size):
                yield list[batch_size * i: batch_size * (i + 1)]
        for i, batch_ in tqdm(enumerate(zip(batch(instructions), batch(inputs))),total=(len(instructions)-1)//batch_size+1):
            instructions, inputs = batch_
            output = evaluate(instructions, inputs)
            outputs = outputs + output
        try:
            for i, test in tqdm(enumerate(test_data)):
                test_data[i]['predict'] = outputs[i]
        except:
            ipdb.set_trace()
            
        result_json_data = f'games_client{client_idx}.json'

        if client_idx == 0:
            result = test_data
            with open(result_json_data, 'w') as f:
                json.dump(result, f, indent=4)
        else:
            result.extend(test_data)
            with open(result_json_data, 'w') as f:
                json.dump(result, f, indent=4)        
        del model      

def generate_prompt(instruction, input=None):
    if input:
        return f"""Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.  

### Instruction:
{instruction}

### Input:
{input}

### Response:
"""
    else:
        return f"""Below is an instruction that describes a task. Write a response that appropriately completes the request.  

### Instruction:
{instruction}

### Response:
"""


if __name__ == "__main__":
    fire.Fire(main)