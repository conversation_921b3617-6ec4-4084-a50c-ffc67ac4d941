# FELLRec 完整使用指南

## 🎯 项目简介

FELLRec (Federated Framework for LLM-based Recommendation) 是一个创新的联邦学习推荐系统，结合了大语言模型的强大能力和联邦学习的隐私保护特性。

### 🌟 核心优势
- **隐私保护**：用户数据不离开本地设备
- **智能推荐**：利用大语言模型的理解能力  
- **高效训练**：使用LoRA技术降低计算成本
- **可扩展性**：支持多客户端分布式训练

## 📁 项目结构总览

```
FELLRec-main/
├── 📁 BigRec_FELLRec/              # 基于LLaMA的实现 ⭐⭐⭐
│   ├── 📄 finetune.py              # 核心训练脚本
│   ├── 📄 utils.py                 # 工具函数集合
│   ├── 📄 inference.py             # 推理脚本
│   ├── 📄 train.sh                 # 训练启动脚本
│   ├── 📄 accelerate.yaml          # 分布式配置
│   └── 📁 data/                    # 数据目录
├── 📁 RecFormer_FELLRec/           # 基于RecFormer的实现 ⭐⭐
│   ├── 📄 finetune.py              # RecFormer训练脚本
│   ├── 📄 utils.py                 # RecFormer工具函数
│   └── 📁 recformer/               # RecFormer模型定义
├── 📄 README.md                    # 项目说明
└── 📁 data/                        # 共享数据目录
```

## 🚀 快速开始

### 第一步：环境准备

#### 1.1 创建虚拟环境
```bash
# 使用conda创建环境
conda create -n fellrec python=3.8
conda activate fellrec

# 或使用venv
python -m venv fellrec_env
source fellrec_env/bin/activate  # Linux/Mac
# fellrec_env\Scripts\activate   # Windows
```

#### 1.2 安装依赖
```bash
cd BigRec_FELLRec
pip install -r requirements.txt

# 核心依赖包括：
# torch>=2.1.1
# transformers>=4.30.0
# peft>=0.4.0
# accelerate>=0.20.0
# datasets>=2.12.0
# scikit-learn>=1.3.0
```

#### 1.3 配置加速器
```bash
# 配置Accelerate分布式训练
accelerate config

# 或直接使用提供的配置文件
cp accelerate.yaml ~/.cache/huggingface/accelerate/default_config.yaml
```

### 第二步：数据准备

#### 2.1 数据目录结构
```bash
# 确保数据目录结构正确
BigRec_FELLRec/data/games/
├── train_1024_user.json      # 训练数据
├── valid_5000_user.json      # 验证数据  
├── test_user.json            # 测试数据
├── meta_data.json            # 物品元数据
└── group_ckpt.pth.tar        # 预训练用户嵌入
```

#### 2.2 数据格式检查
```python
# 检查数据格式是否正确
import json

def check_data_format(file_path):
    with open(file_path, 'r') as f:
        sample = json.loads(f.readline())
        required_keys = ['instruction', 'input', 'output', 'user']
        for key in required_keys:
            assert key in sample, f"缺失字段: {key}"
    print(f"✅ {file_path} 格式正确")

check_data_format('data/games/train_1024_user.json')
```

### 第三步：模型准备

#### 3.1 下载LLaMA模型
```bash
# 方法1：从HuggingFace下载
# 需要申请访问权限
git lfs install
git clone https://huggingface.co/meta-llama/Llama-2-7b-hf

# 方法2：使用本地模型
# 将模型路径设置为本地目录
```

#### 3.2 修改配置文件
```bash
# 编辑 train.sh 文件
vim train.sh

# 修改以下行：
--base_model "path/to/your/llama/model" \
```

### 第四步：开始训练

#### 4.1 使用脚本训练（推荐）
```bash
cd BigRec_FELLRec

# 修改train.sh中的模型路径后执行
bash train.sh
```

#### 4.2 直接命令行训练
```bash
python finetune.py \
    --base_model "path/to/llama-7b" \
    --train_data_path '["./data/games/train_1024_user.json"]' \
    --val_data_path '["./data/games/valid_5000_user.json"]' \
    --test_data_path '["./data/games/test_user.json"]' \
    --output_dir ./model/games/experiment_1 \
    --client_num 5 \
    --num_epochs 10 \
    --learning_rate 1e-4 \
    --batch_size 64 \
    --micro_batch_size 4
```

### 第五步：监控训练

#### 5.1 查看训练日志
```bash
# 实时查看训练日志
tail -f training.log

# 查看GPU使用情况
nvidia-smi -l 1
```

#### 5.2 训练进度指标
```
正常训练日志示例：
🚀 开始联邦学习训练，共 10 个epoch
📅 第 1/10 轮训练开始
🔧 正在准备客户端 0 的模型...
🏃‍♂️ 开始训练客户端 0，第 1 轮
✅ 客户端 0 训练完成，最终损失: 2.3456
🔄 开始第 1 轮的联邦模型聚合...
🌍 第 1 轮全局评估损失: 2.1234
```

### 第六步：模型推理

#### 6.1 生成推荐结果
```bash
# 使用训练好的模型进行推理
python inference.py \
    --base_model "path/to/llama-7b" \
    --test_data_path "data/games/test.json"
```

#### 6.2 推理结果格式
```json
{
    "instruction": "根据用户的游戏历史，推荐下一个可能喜欢的游戏",
    "input": "用户玩过：《塞尔达传说》、《超级马里奥》",
    "output": "推荐：《马里奥卡丁车》",
    "predict": "推荐：《马里奥卡丁车》、《动物森友会》"
}
```

### 第七步：性能评估

#### 7.1 运行评估脚本
```bash
cd BigRec_FELLRec/data/games

# 评估推理结果
python evaluate.py --input_dir "../../推理结果目录"
```

#### 7.2 评估指标解读
```
评估结果示例：
📊 推荐性能评估结果：
- Top-1 准确率: 15.2%
- Top-5 准确率: 32.8%  
- Top-10 准确率: 45.6%
- Hit Rate@10: 0.456
- NDCG@10: 0.234
```

## ⚙️ 高级配置

### 联邦学习参数调优

#### 客户端数量选择
```python
# 客户端数量对性能的影响
client_num_options = [3, 5, 7, 10]
for client_num in client_num_options:
    # 运行实验并记录结果
    run_experiment(client_num=client_num)
```

#### 聚合策略优化
```python
# 调整聚合权重参数
alpha_options = [0.5, 0.7, 0.9]  # 聚合权重参数
beta_options = [1, 2, 3]         # 权重调节参数

for alpha in alpha_options:
    for beta in beta_options:
        run_experiment(alpha=alpha, beta=beta)
```

### LoRA参数调优

#### 秩（Rank）选择
```python
# LoRA秩对性能和效率的影响
lora_r_options = [4, 8, 16, 32]
for r in lora_r_options:
    # 更大的秩 = 更多参数 = 更好性能但更高成本
    run_experiment(lora_r=r)
```

#### 目标模块选择
```python
# 不同的目标模块组合
target_modules_options = [
    ["q_proj", "v_proj"],                    # 基础配置
    ["q_proj", "v_proj", "k_proj"],         # 增加key投影
    ["q_proj", "v_proj", "o_proj"],         # 增加输出投影
    ["q_proj", "v_proj", "gate_proj"]       # 增加门控投影
]
```

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 内存不足
```
错误：CUDA out of memory
解决方案：
- 减少batch_size: 64 → 32 → 16
- 减少client_num: 5 → 3
- 启用梯度累积
- 使用更小的模型
```

#### 2. 模型加载失败
```
错误：Can't load tokenizer
解决方案：
- 检查base_model路径是否正确
- 确保网络连接正常
- 使用本地模型路径
- 检查HuggingFace token
```

#### 3. 分布式训练失败
```
错误：Address already in use
解决方案：
- 修改accelerate.yaml中的端口号
- 杀死占用端口的进程
- 重启训练进程
```

#### 4. 数据加载错误
```
错误：FileNotFoundError
解决方案：
- 检查数据文件路径
- 确保数据格式正确
- 验证文件权限
- 检查磁盘空间
```

## 📊 实验设计建议

### 基础对比实验
1. **联邦 vs 集中式**：比较隐私保护效果
2. **不同客户端数量**：找到最优配置
3. **不同聚合策略**：测试算法改进
4. **LoRA vs 全参数微调**：效率对比

### 消融实验
1. **移除相似度聚合**：验证聚合策略效果
2. **移除动态权重**：验证权重调整机制
3. **不同数据分割方式**：验证聚类分割效果

### 扩展实验
1. **不同数据集**：测试泛化能力
2. **不同基础模型**：LLaMA vs ChatGLM vs Baichuan
3. **不同推荐任务**：电影、音乐、商品推荐

## 📚 进阶学习资源

### 核心论文
- **联邦学习**：McMahan et al. "Communication-Efficient Learning"
- **LoRA**：Hu et al. "LoRA: Low-Rank Adaptation"  
- **推荐系统**：Kang et al. "Self-Attentive Sequential Recommendation"

### 技术文档
- [HuggingFace Transformers](https://huggingface.co/docs/transformers)
- [PEFT Documentation](https://huggingface.co/docs/peft)
- [PyTorch Distributed](https://pytorch.org/tutorials/intermediate/ddp_tutorial.html)

### 开源项目
- [FederatedScope](https://github.com/alibaba/FederatedScope)
- [FedML](https://github.com/FedML-AI/FedML)
- [OpenFedLLM](https://github.com/rui-ye/OpenFedLLM)

## 🎓 总结

FELLRec为联邦学习推荐系统研究提供了完整的解决方案。通过本指南，您可以：

1. ✅ 快速搭建实验环境
2. ✅ 理解项目架构和数据流
3. ✅ 掌握训练和评估流程  
4. ✅ 进行高级参数调优
5. ✅ 解决常见技术问题
6. ✅ 设计科学的对比实验

祝您在联邦学习推荐系统研究中取得优异成果！🚀
