# FELLRec项目详细说明文档

## 项目概述

FELLRec (Federated Framework for LLM-based Recommendation) 是一个基于大语言模型的联邦学习推荐系统。该项目结合了联邦学习的隐私保护特性和大语言模型的强大表示能力，为推荐系统提供了一个创新的解决方案。

## 核心技术

### 1. 联邦学习 (Federated Learning)
- **隐私保护**: 用户数据不离开本地设备，只共享模型参数
- **分布式训练**: 多个客户端并行训练，提高训练效率
- **模型聚合**: 通过智能聚合策略整合各客户端的模型更新

### 2. 大语言模型 (Large Language Models)
- **LLaMA基础**: 使用Meta的LLaMA作为基础模型
- **RecFormer**: 专门为序列推荐设计的Transformer架构
- **参数高效微调**: 使用LoRA技术减少训练成本

### 3. 推荐系统
- **序列推荐**: 基于用户历史行为序列进行推荐
- **个性化**: 为每个用户提供个性化的推荐结果
- **可解释性**: 利用大语言模型的文本生成能力提供推荐解释

## 项目结构详解

### BigRec_FELLRec/ (基于LLaMA的实现)

#### 核心文件说明

**finetune.py** - 联邦训练主脚本
- 实现多客户端联邦学习训练循环
- 支持LoRA参数高效微调
- 包含模型聚合和早停机制
- 使用相似度矩阵进行智能聚合

**utils.py** - 工具函数集合
- `split_dataset()`: 基于用户嵌入的数据分割
- `aggregate()`: 模型聚合函数
- `cluster_clients()`: 客户端聚类
- `split_client_server()`: 客户端-服务器模型分割

**inference.py** - 推理脚本
- 加载训练好的模型进行推理
- 支持批量推理和结果保存
- 处理多客户端模型的推理流程

**train.sh** - 训练启动脚本
- 配置训练超参数
- 支持网格搜索
- 使用accelerate进行分布式训练

### RecFormer_FELLRec/ (基于RecFormer的实现)

#### 核心组件

**recformer/** - RecFormer模型定义
- 专门为推荐任务设计的Transformer架构
- 支持序列建模和物品表示学习
- 包含客户端-服务器分割版本

**finetune.py** - RecFormer训练脚本
- 实现基于RecFormer的联邦学习
- 包含数据加载和预处理
- 支持分布式训练和模型评估

## 关键算法

### 1. 联邦聚合算法

```python
def get_aggregate_lora_weight(client_index, sim_matrix, accumulated_params, weight, beta):
    """
    基于相似度矩阵的联邦聚合算法
    
    核心思想：
    1. 计算客户端之间的相似度
    2. 根据相似度和训练损失调整聚合权重
    3. 加权平均得到聚合后的模型参数
    """
```

### 2. 客户端分割策略

```python
def split_dataset(train_data, n, val_data, test_data, pretrain_emb_path):
    """
    基于用户嵌入的数据分割策略
    
    步骤：
    1. 加载预训练用户嵌入
    2. 使用K-means聚类将用户分组
    3. 将相似用户分配到同一客户端
    4. 确保数据分布的合理性
    """
```

### 3. 模型分割机制

```python
def split_client_server(original_model, k):
    """
    客户端-服务器模型分割
    
    策略：
    - 前k层 + 最后一层 → 客户端
    - 中间层 → 服务器
    - 减少客户端计算负担
    - 保护模型隐私
    """
```

## 训练流程

### 1. 数据准备
1. 加载原始推荐数据
2. 基于用户嵌入进行聚类
3. 将用户分配到不同客户端
4. 生成客户端专属数据集

### 2. 模型初始化
1. 加载预训练基础模型（LLaMA/RecFormer）
2. 添加LoRA适配器
3. 执行客户端-服务器分割
4. 初始化联邦学习参数

### 3. 联邦训练循环
```
for epoch in range(num_epochs):
    for client in clients:
        # 本地训练
        local_model = train_locally(client_data)
        
        # 计算相似度
        similarity = compute_similarity(local_models)
        
        # 模型聚合
        global_model = aggregate(local_models, similarity)
        
        # 模型分发
        distribute_model(global_model)
```

### 4. 评估和推理
1. 在验证集上评估模型性能
2. 选择最佳模型进行推理
3. 生成推荐结果和解释

## 使用指南

### 环境配置
```bash
# 安装依赖
pip install torch transformers peft datasets sklearn

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0
export LD_LIBRARY_PATH=""
```

### 快速开始

#### 方法1: 使用BigRec (LLaMA)
```bash
cd BigRec_FELLRec
# 修改train.sh中的base_model路径
bash train.sh
```

#### 方法2: 使用RecFormer
```bash
cd RecFormer_FELLRec
# 下载预训练模型
bash finetune.sh
```

### 参数配置说明

**联邦学习参数**
- `client_num`: 客户端数量 (默认: 5)
- `round`: 每轮本地训练epoch数 (默认: 5)
- `alpha`: 聚合权重参数 (默认: 0.7)
- `beta`: 权重调节参数 (默认: 1)

**LoRA参数**
- `lora_r`: LoRA秩 (默认: 8)
- `lora_alpha`: LoRA缩放参数 (默认: 16)
- `lora_dropout`: LoRA dropout率 (默认: 0.05)

**训练参数**
- `learning_rate`: 学习率 (默认: 1e-4)
- `batch_size`: 批次大小 (默认: 64)
- `num_epochs`: 训练轮数 (默认: 75)

## 实验结果

### 数据集
- **Games**: 游戏推荐数据集
- 包含用户行为序列和物品元数据
- 支持序列推荐任务评估

### 评估指标
- **准确率**: Top-K推荐准确率
- **召回率**: Top-K推荐召回率
- **NDCG**: 归一化折损累积增益
- **训练效率**: 联邦学习vs集中式学习

## 扩展和定制

### 添加新的基础模型
1. 在`models.py`中定义新模型类
2. 实现相应的tokenizer
3. 修改训练脚本中的模型加载逻辑

### 自定义聚合策略
1. 在`utils.py`中实现新的聚合函数
2. 修改`aggregate()`函数调用
3. 调整相似度计算方法

### 支持新数据集
1. 实现数据加载器
2. 定义数据预处理流程
3. 修改评估指标计算

## 常见问题

### Q: 如何调整客户端数量？
A: 修改`client_num`参数，同时确保数据分割合理。

### Q: 内存不足怎么办？
A: 减少`batch_size`，启用8位量化，或使用梯度检查点。

### Q: 如何提高推荐质量？
A: 调整LoRA参数，增加训练轮数，或优化聚合策略。

### Q: 支持哪些基础模型？
A: 目前支持LLaMA和RecFormer，可扩展到其他Transformer模型。

## 贡献指南

欢迎提交Issue和Pull Request！

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 发起Pull Request

## 许可证

本项目采用MIT许可证，详见LICENSE文件。
