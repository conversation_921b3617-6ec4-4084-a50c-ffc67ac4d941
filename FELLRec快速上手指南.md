# FELLRec 快速上手指南

## 📁 项目模块分布

### 整体架构
```
FELLRec-main/
├── 🏢 BigRec_FELLRec/          # 基于LLaMA的主要实现（推荐使用）
├── 🏢 RecFormer_FELLRec/       # 基于RecFormer的备选实现
└── 📁 data/                    # 共享数据目录
```

### BigRec_FELLRec/ - 主要实现目录 ⭐
```
BigRec_FELLRec/
├── 🎯 finetune.py             # 联邦学习训练主脚本
├── 🔧 utils.py                # 联邦学习核心算法（数据分割、模型聚合）
├── 🔮 inference.py            # 推理生成脚本，加载训练好的模型生成推荐
├── 🚀 train.sh                # 一键训练启动脚本
├── ⚙️ accelerate.yaml         # 多GPU分布式训练配置
├── 📋 requirements.txt        # Python依赖包列表
└── 📁 data/games/             # 游戏推荐数据集
```

### RecFormer_FELLRec/ - 备选实现
基于RecFormer模型的联邦学习实现，功能类似但使用不同的基础模型架构。

### data/ - 数据目录结构
```
data/games/
├── 📄 train_1024_user.json    # 训练数据（1024个用户）
├── 📄 valid_5000_user.json    # 验证数据（5000个用户）
├── 📄 test_user.json          # 测试数据
├── 📄 meta_data.json          # 游戏元数据信息
├── 📄 group_ckpt.pth.tar      # 预训练用户嵌入（用于智能数据分割）
└── 🔍 evaluate.py             # 推荐性能评估脚本
```

## 🚀 快速上手流程

### 第一步：环境安装
```bash
# 创建虚拟环境
conda create -n fellrec python=3.8
conda activate fellrec

# 安装依赖
cd BigRec_FELLRec
pip install -r requirements.txt
```

### 第二步：数据准备
```bash
# 验证数据文件存在
ls data/games/
# 应包含：train_*.json, valid_*.json, test_*.json, group_ckpt.pth.tar

# 准备LLaMA模型（下载或使用本地路径）
# 例如：/path/to/llama-7b-hf
```

### 第三步：训练
```bash
# 修改train.sh中的模型路径
vim train.sh
# 将 --base_model " " 改为你的LLaMA模型路径

# 启动训练
bash train.sh
```

### 第四步：推理
```bash
# 生成推荐结果
python inference.py --base_model "path/to/llama-7b"

# 评估性能
cd data/games
python evaluate.py --input_dir "../../"
```

## 🔧 最简运行示例

快速验证环境是否正常：

```bash
# 小规模测试（约5分钟）
python finetune.py \
    --base_model "path/to/llama-7b" \
    --sample 50 \
    --num_epochs 1 \
    --client_num 2 \
    --batch_size 16

# 如果成功完成，说明环境配置正确
```

## 💡 核心概念

- **联邦学习**：多个客户端使用本地数据训练，只共享模型参数，保护数据隐私
- **LoRA微调**：只训练少量参数（1-2%），大幅降低计算成本
- **智能聚合**：基于客户端相似度进行模型参数聚合，提高推荐效果

## 📊 预期输出

训练成功后会生成：
- `model/games/best_client*_model/`：各客户端的最佳模型
- `games_client*.json`：推理结果文件
- `training.log`：详细训练日志

评估结果示例：
```
📊 推荐性能评估结果：
- Top-1 准确率: 15.2%
- Top-5 准确率: 32.8%
- Top-10 准确率: 45.6%
```

---
**注意**：完整的故障排除、性能优化和进阶配置请参考 `FELLRec项目完整架构与运行指南.md`
